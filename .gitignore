# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
venv/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dist/
build/

# Java
*.class
*.jar
*.war
*.ear
*.nar
*.car
*.zip
*.tar.gz
*.rar

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS-specific files
.DS_Store
Thumbs.db

# IDEs and Editors
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Docker
*.env
docker-compose.override.yml
Dockerfile

# Miscellaneous
*.swp
*.swo
*.tmp
*.bak
*.backup
*.orig
