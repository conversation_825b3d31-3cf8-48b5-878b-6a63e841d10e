---
description: 
globs: 
alwaysApply: true
---
# Coding Conventions

## General
*   Maintain a consistent coding style throughout the project.
*   Write clear and concise comments where necessary, especially for complex logic.
*   Aim for readability and maintainability.
*   Use meaningful names for variables, functions, and classes.

## JavaScript (JS)
*   Follow modern JavaScript (ES6+) best practices.
*   Use `const` for variables that won't be reassigned, and `let` for variables that will. Avoid `var`.
*   Prefer arrow functions for conciseness where appropriate.
*   Use strict equality (`===` and `!==`) over loose equality (`==` and `!=`).
*   Organize code into modules ([src/modules/](mdc:src/modules)) for better structure.
*   Utilize utility functions from [src/utils/](mdc:src/utils) for common tasks.

## HTML5
*   Use semantic HTML5 tags to structure content meaningfully (e.g., `<article>`, `<aside>`, `<nav>`, `<section>`).
*   Ensure HTML is well-formed and valid.
*   Use ARIA attributes to enhance accessibility where needed.
*   Keep `id` attributes unique on a page.
*   Minimize inline styles; prefer external stylesheets in [src/styles/](mdc:src/styles).

## CSS3
*   Organize CSS logically, potentially using a methodology like BEM (Block, Element, Modifier) or a similar approach if the team agrees.
*   Use classes for styling rather than IDs where possible to promote reusability.
*   Write selectors that are efficient and not overly specific.
*   Use relative units (em, rem, %) for responsive design where appropriate.
*   Store stylesheets in [src/styles/](mdc:src/styles).

## Bootstrap 5
*   Utilize Bootstrap 5 classes for layout, components, and utilities to maintain consistency and speed up development.
*   Customize Bootstrap variables if needed, rather than overriding styles with high specificity.
*   Refer to the official Bootstrap 5 documentation for class usage and component structure.
*   When creating custom components in [src/components/](mdc:src/components), ensure they integrate well with Bootstrap's styling and grid system.
