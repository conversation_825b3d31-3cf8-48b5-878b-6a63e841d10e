---
description: 
globs: 
alwaysApply: true
---
# Project Structure Guide

This project is a web application, likely built with Vite.

## Key Files and Directories:

*   **Entry Point**: [index.html](mdc:index.html) is the main HTML file.
*   **Configuration**:
    *   [package.json](mdc:package.json) defines project dependencies and scripts.
    *   [vite.config.js](mdc:vite.config.js) is the main Vite configuration file.
    *   [vite.config.extension.mjs](mdc:vite.config.extension.mjs) is likely a Vite configuration specific to a browser extension.
*   **Source Code (`src/`)**:
    *   [src/components/](mdc:src/components) contains reusable UI components.
    *   [src/classes/](mdc:src/classes) likely holds class definitions.
    *   [src/constants/](mdc:src/constants) is for defining constant values.
    *   [src/modules/](mdc:src/modules) probably contains different application modules or features, being imported to classes.
    *   [src/styles/](mdc:src/styles) is for stylesheets (CSS).
    *   [src/utils/](mdc:src/utils) contains utility functions.
*   **CI/CD**:
    *   [.github/workflows/](mdc:.github/workflows) contains GitHub Actions workflows for automation.
*   **Documentation**:
    *   [README.md](mdc:README.md) provides an overview of the project.
    *   [wikis/](mdc:wikis) may contain more detailed documentation or wiki pages.
    *   [documents/tasks/](mdc:documents/tasks) contain the low-level break down details for tasks

## Development Workflow:

1.  Install dependencies using `npm install` (or a similar package manager command based on `package.json`).
2.  Run the development server, likely using a script defined in `package.json` such as `npm run dev` which would utilize `vite.config.js`.
3.  Modify code primarily within the `src/` directory.
