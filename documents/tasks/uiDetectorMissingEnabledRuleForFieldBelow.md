# Task: Implement Missing `gr.enable` Rule Check for `fieldsBelow`

**Objective:** Extend the `UiBugsDetector` tool to identify instances where a `fieldBelow` is declared within a widget's `ui:multipleOption` but lacks a corresponding `gr.enable` logic rule in the form's definition.

**Background:**
Some widgets, like "Radio" or "MultipleCheckbox", use `ui:multipleOption` to define different choices. Each choice can specify `fieldsBelow`, which are other form fields that should ideally be conditionally enabled/disabled based on the selection of that option. The current task focuses on ensuring that if a `fieldBelow` is specified, there's an explicit rule using `gr.enable(...)` to manage its enabled state, linked to its parent widget.

*   **Example of missing `gr.enable` logic (Conceptual, based on `v17.json` if checking strictly for `gr.enable`):** In `v17.json`, `multiplecheckbox5` has `fieldbelow1` in its `fieldsBelow`. While `Rule41` controls its visibility using `afs.logic.main.fieldbelow1.hide()`, it would be flagged by this new check if we are *strictly* looking for `gr.enable`.
*   **Example of correct `gr.enable` logic (`v18.json`):** `v18.json` includes "Rule 144" which correctly uses `gr.enable(logic.fieldbelow1, ...)` and `gr.enable(logic.commoditydoesnothavecpo, ...)` controlled by `multiplecheckbox5`.

**Target Array for Issues:** `issues.missingEnabledRuleForFieldBelow`

**Access to Form Rules:** `const {rules} = jsonData.formData.form;`

## Low-Level Implementation Details:

The implementation will primarily involve modifications within the `src/modules/uiBugsDetector.js` file.

### 1. Data Collection: Identify Parent Widgets and their `fieldsBelow`

*   **Location:** Inside the `analyzeSchemaObject(key, widget, schemaTOC, issues)` method, or a new dedicated method called from it.
*   **Logic:**
    *   Iterate through each widget (`widget`) and its alias (`key`) in the `uiSchema`.
    *   Check if the widget possesses the `WIDGET_PROPERTIES.UI_MULTIPLE_OPTION` property.
        ```javascript
        const multipleOption = lodash.get(widget, WIDGET_PROPERTIES.UI_MULTIPLE_OPTION);
        if (multipleOption && multipleOption.options) {
            // Proceed to check options
        }
        ```
    *   If `ui:multipleOption` exists, iterate through its `options` array. Each option is typically an array `[optionValue, optionDetails]`.
        ```javascript
        for (const option of multipleOption.options) {
            const optionDetails = option[1];
            const fieldsBelow = lodash.get(optionDetails, OPTION_PROPERTIES.FIELDS_BELOW);
            // ...
        }
        ```
    *   If an `optionDetails` object contains a `fieldsBelow` property (which is an array of field aliases):
        *   For each `fieldAlias` in the `fieldsBelow` array:
            *   Store this `fieldAlias` along with its `parentWidgetKey` (which is `key` from the `uiSchema` iteration).
            *   A good structure for this would be an array of objects:
                ```javascript
                // This array should be initialized before the uiSchema loop,
                // or collected and passed to a new checking function.
                // For example, within analyzeSchemaObject:
                // if (!this.potentialMissingEnableRules) { this.potentialMissingEnableRules = []; }
                // this.potentialMissingEnableRules.push({ parentWidgetKey: key, fieldBelowAlias: fieldAlias, parentWidgetType: widgetType });

                // Or, more directly:
                let collectedFieldsBelow = []; // To be populated in this step
                // ...
                if (fieldsBelow && Array.isArray(fieldsBelow)) {
                    for (const fieldBelowAlias of fieldsBelow) {
                        collectedFieldsBelow.push({
                            parentWidgetKey: key, // e.g., "multiplecheckbox5"
                            fieldBelowAlias: fieldBelowAlias // e.g., "fieldbelow1"
                        });
                    }
                }
                ```

### 2. Rule Verification: Check for `gr.enable` Logic

*   Create a new method, e.g., `checkMissingEnabledRuleForFieldsBelow(collectedFieldsBelow, formRules, issues)`.
    *   `collectedFieldsBelow`: The array of objects from Step 1.
    *   `formRules`: The `rules` array from `jsonData.formData.form.rules`.
    *   `issues`: The main issues object to push findings into.
*   **Logic:**
    *   Iterate through each item in `collectedFieldsBelow` (e.g., `{ parentWidgetKey, fieldBelowAlias }`).
    *   For each item, assume a rule doesn't exist yet: `let ruleExistsForFieldBelow = false;`
    *   Iterate through the `formRules` array. For each `rule`:
        *   Let `ruleLogic = rule.value;` (this is the string containing the rule's code).
        *   Check for two specific patterns within `ruleLogic`:
            1.  **Parent Widget Involvement:** The rule function signature must include the `parentWidgetKey`.
                *   Pattern: `function(...${parentWidgetKey}...)`
                *   Regex example: `new RegExp(\`function\\s*\\(([^)]*\\b${parentWidgetKey}\\b[^)]*)\\)\`)`
            2.  **`gr.enable` Usage for the `fieldBelowAlias`:** The rule logic must contain `gr.enable` targeting the `fieldBelowAlias`.
                *   Pattern: `gr.enable(logic.${fieldBelowAlias}` or `gr.enable(afs.logic.main.${fieldBelowAlias}`
                *   Regex example: `new RegExp(\`gr\\\\.enable\\(\\s*(afs\\\\.logic\\\\.main|logic)\\\\.${fieldBelowAlias}\\b\`)`

        *   If **both** patterns are found in the `ruleLogic`:
            *   Set `ruleExistsForFieldBelow = true;`
            *   Break the inner loop (no need to check other rules for this specific `fieldBelowAlias`).
    *   After iterating through all `formRules`, if `ruleExistsForFieldBelow` is still `false`:
        *   This indicates a missing `gr.enable` rule. Add it to the `issues` object:
            ```javascript
            issues.missingEnabledRuleForFieldBelow.push({
                alias: fieldBelowAlias,
                note: `Parent widget: <code>${parentWidgetKey}</code> - No corresponding 'gr.enable' rule found for fieldBelow: <code>${fieldBelowAlias}</code>`
            });
            ```

### 3. Integration into `UiBugsDetector`

*   **Initialization:** Ensure `missingEnabledRuleForFieldBelow: []` is part of the `issues` object initialized in the `analyze` method.
    ```javascript
    let issues = {
        // ... other issue types
        missingEnabledRuleForFieldBelow: [],
    };
    ```
*   **Execution Flow:**
    1.  In the `analyze` method, after iterating through `uiSchema` with `analyzeSchemaObject` (or if integrating Step 1 directly into `analyzeSchemaObject`, collect all `collectedFieldsBelow` there).
    2.  Call the new `checkMissingEnabledRuleForFieldsBelow` method, passing the collected data and the form's rules.
        ```javascript
        // In analyze method:
        // const { uiSchema } = jsonData.formData.form.namespaceFormSchemaMap.main;
        // const { rules } = jsonData.formData.form;
        // let allFieldsBelowToCheck = [];

        // // Option A: Populate allFieldsBelowToCheck within analyzeSchemaObject and store it on `this`
        // // or return it from analyzeSchemaObject if analyzeSchemaObject is refactored to collect these.

        // // Option B: If Step 1 logic is separate:
        // allFieldsBelowToCheck = this.collectAllFieldsBelow(uiSchema); // New helper method

        // this.checkMissingEnabledRuleForFieldsBelow(allFieldsBelowToCheck, rules, issues);
        ```

    *If Step 1 (Data Collection) is implemented directly within `analyzeSchemaObject`*:
    This is less ideal as it would mix concerns. It's cleaner to have `analyzeSchemaObject` focus on individual widgets and a separate step aggregate and then check rules.
    However, if done within `analyzeSchemaObject`, after identifying a `fieldBelowAlias` and its `parentWidgetKey`, you would immediately call a helper to check against all `rules`. This might be less efficient if many `fieldsBelow` point to the same parent, causing redundant rule scanning. The two-step approach (collect all, then check all) is generally better.

### Example `v18.json` rule to match ("Rule 144"):
```json
{
    "value": "function(multiplecheckbox5)\n\nlocal gr = import \"gr_v2\";\n\nlocal logic = afs.logic.main;\n\nlocal v = multiplecheckbox5.value;\n\nlocal op1 = v == \"1\";\nlocal op2 = v == \"2\";\n\nstd.flattenArrays([\n    gr.enable(logic.fieldbelow1, op1, [\n        logic.fieldbelow1,\n    ]),\n    gr.enable(logic.commoditydoesnothavecpo, op2, [\n        logic.commoditydoesnothavecpo,\n    ]),\n])\n\n// ... comments ...\n",
    "name": "[Radio] Show/Hide Enable/Disable by option - Rule 144",
    // ...
}
```
For `parentWidgetKey = "multiplecheckbox5"` and `fieldBelowAlias = "fieldbelow1"`:
1.  `function(multiplecheckbox5)` pattern matches.
2.  `gr.enable(logic.fieldbelow1` pattern matches.
Result: Rule exists.

### Testing Considerations:

*   **`v17.json`**:
    *   `multiplecheckbox5` has `fieldsBelow: ["fieldbelow1"]`.
    *   `Rule41` in `v17.json` is: `function(multiplecheckbox5)\n...\nafs.logic.main.fieldbelow1.hide(!op1)\n...`
    *   This rule mentions `multiplecheckbox5` and `fieldbelow1`. However, it uses `.hide()` not `gr.enable()`.
    *   Expected: `fieldbelow1` should be reported as missing a `gr.enable` rule.
*   **`v18.json`**:
    *   `multiplecheckbox5` has `fieldsBelow: ["fieldbelow1"]` in one option and `fieldsBelow: ["commoditydoesnothavecpo"]` in another.
    *   `Rule 144` uses `gr.enable(logic.fieldbelow1, ...)` and `gr.enable(logic.commoditydoesnothavecpo, ...)` and its function signature is `function(multiplecheckbox5)`.
    *   Expected: No issues should be reported for `fieldbelow1` or `commoditydoesnothavecpo` with respect to `multiplecheckbox5`.
*   Create test cases with no rules at all for a `fieldBelow`.
*   Create test cases where a rule mentions the parent widget but not the `gr.enable` for the specific `fieldBelow`.
*   Create test cases where a rule might use `gr.enable` for the field but is not linked to the correct parent widget in its function signature.

This detailed breakdown should provide a clear path for implementing the new check. 