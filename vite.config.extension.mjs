import { defineConfig } from 'vite';
import { resolve } from 'path';
import { viteStaticCopy } from 'vite-plugin-static-copy';

// Not used for now
export default defineConfig({
    build: {
        outDir: 'dist/[unused]chrome-extension',
        rollupOptions: {
            input: {
                background: resolve('src/[unused]chrome-extension/background.modules'),
                contentScript: resolve('src/[unused]chrome-extension/contentScript.modules'),
            },
            output: {
                entryFileNames: '[name].modules',
                chunkFileNames: '[name].modules',
                assetFileNames: '[name].[ext]'
            }
        },
    },
    plugins: [
        // will need to install vite-plugin-static-copy
        viteStaticCopy({
            targets: [
                {
                    src: 'src/[unused]chrome-extension/manifest.json',
                    dest: '.'
                },
                {
                    src: 'src/[unused]chrome-extension/icons',
                    dest: '.'
                },
                {
                    src: 'src/[unused]chrome-extension/rules.json',
                    dest: '.'
                },
                {
                    src: 'src/[unused]chrome-extension/fetch-interceptor.modules',
                    dest: '.'
                }
            ]
        })
    ]
});