import formToBlueprint from "../modules/formToBlueprint";
import {changeChevronIcon, copyToClipboardByClass} from "../utils/commonHelpers";
import {getForm} from "../utils/apiRequestHandler";
import {getBlueprint, getBlueprintCatalaVersion} from "../utils/blueprintApiRequestHandler";

class FormToBlueprintManager {
    constructor(transferBtn, resultsContentId) {
        this.transferBtn = document.getElementById(transferBtn);
        this.widgetSelection = document.getElementById('widgetSelection');
        this.widgetTypeButton = document.getElementById('widgetTypeButton');
        this.widgetTypeInput = document.getElementById('widgetType');
        this.aliasesContainer = document.getElementById('aliases').closest('.col-md-6');

        this.resultsContent = document.getElementById(resultsContentId);
    }

    initialize() {
        if (this.transferBtn) {
            this.transferBtn.addEventListener('click', this.handleClick.bind(this));
        }
        if(this.widgetTypeButton) {
            this.widgetSelection.addEventListener('click', this.selectWidgetType.bind(this));
        }
           this.aliasesContainer.style.display = 'none';
    }

    selectWidgetType(event) {
        if (event.target.classList.contains('dropdown-item')) {
            const selectedValue = event.target.getAttribute('data-value');
            const selectedText = event.target.textContent;
            this.widgetTypeButton.textContent = selectedText;
            this.widgetTypeInput.value = selectedValue;
            this.aliasesContainer.style.display = 'block';

            // Show/hide aliases field based on widget type
            if (selectedValue === 'GATING_QUESTION' || selectedValue === 'WELL') {
                 document.querySelector('label[for="aliases"]').innerHTML = 'Aliases (Input alias of group if you want to transfer all the fields in the group) <span class="text-danger">*</span>';
            } else  {
                document.querySelector('label[for="aliases"]').innerHTML = 'Aliases <span class="text-info">(Leave empty if you want to transfer all the fields. Input alias of group if you want to transfer all the fields in the group)</span>';
            }

            if(selectedValue) {
                document.getElementById('widgetTypeError').textContent = '';
            }
        }
    }

    parseAliases(aliasesString) {
        if (!aliasesString || aliasesString.trim() === '') {
            return [];
        }

        return aliasesString
            .split(';')
            .map(alias => alias.trim())
            .filter(alias => alias !== '');
    }

    async handleClick(event) {
        event.preventDefault();
        this.transferBtn.disabled = true;

        try {
            const formVersion = document.getElementById('retroFormVersionId').value || '';
            const formVersionError = document.getElementById('retroFormVersionIdError');
            formVersionError.textContent = '';

            if (formVersion.trim() === '') {
                formVersionError.textContent = 'Please enter a form version!';
                return;
            }

            const blueprintId = document.getElementById('blueprintId').value || '';
            const blueprintIdError = document.getElementById('blueprintIdError');
            blueprintIdError.textContent = '';

            if (blueprintId.trim() === '') {
                blueprintIdError.textContent = 'Please enter a blueprint version!';
                return;
            }

            const widgetType = document.getElementById('widgetType').value || '';
            const aliasesInput = document.getElementById('aliases').value || '';
            const aliasesError = document.getElementById('aliasesError');
            const widgetTypeError = document.getElementById('widgetTypeError');

            //If not selecting widget type, show error
            if(!['GATING_QUESTION', 'SIGNATURE', "FILE_GROUP", "WELL"].includes(widgetType)) {
                widgetTypeError.textContent = 'Please select a widget type!';
                return;
            }
            //If widget type is GATING_QUESTION, then aliases are required
            if ( (this.widgetTypeInput.value === 'GATING_QUESTION' || this.widgetTypeInput.value === 'WELL')
                 && aliasesInput.trim() === '') {
                aliasesError.textContent = 'Please enter at least one alias for Gating Question and Well!';
                return;
            }

            const aliases = this.parseAliases(aliasesInput);

            const form = await getForm(formVersion);
            const blueprint = await getBlueprint(blueprintId);
            const latestVersionId = blueprint.blueprintModel.latestVersionId;
            const blueprintJson = await getBlueprintCatalaVersion(latestVersionId);

            const detector = new formToBlueprint({
                blueprintId,
                latestVersionId,
                widgetType,
                aliases
            });

            const results = await detector.transfer(form, blueprintJson);

            this.resultsContent.innerHTML = detector.renderReport(results);
            this.resultsContent.style.display = 'block';

            changeChevronIcon(this.resultsContent);
        } catch (error) {
            this.displayError(error.message);
        } finally {
            this.transferBtn.disabled = false;
        }
    }

    displayError(message) {
        this.resultsContent.style.display = 'block';
        this.resultsContent.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="bi bi-exclamation-triangle me-2"></i>Error</h4>
                <p class="mb-0">${message}</p>
            </div>
        `;
    }
}

export default FormToBlueprintManager;
