import uiBugsDetector from "../modules/uiBugsDetector";
import {changeChevronIcon, copyToClipboardByClass} from "../utils/commonHelpers";
import {getForm} from "../utils/apiRequestHandler";
import { DOM_LOCATORS, CSS_CLASSES } from '../constants/domLocators';

class UIBugsDetectorManager {
    constructor(analyzeButtonId, resultsContentId) {
        this.analyzeBtn = document.getElementById(analyzeButtonId);
        this.resultsContent = document.getElementById(resultsContentId);
    }

    initialize() {
        if (this.analyzeBtn) {
            this.analyzeBtn.addEventListener('click', this.handleClick.bind(this));
        }
    }

    async handleClick(event) {
        event.preventDefault();
        this.analyzeBtn.disabled = true;

        try {
            const formVersion = document.getElementById(DOM_LOCATORS.TARGET_FORM_VERSION_ID).value.trim() || '';
            const formVersionError = document.getElementById(DOM_LOCATORS.TARGET_FORM_VERSION_ID_ERROR);
            formVersionError.textContent = '';

            if (formVersion.trim() === '') {
                formVersionError.textContent = 'Please enter a form version!';
                return;
            }

            const expectedPageSubtitle = document.getElementById(DOM_LOCATORS.EXPECTED_PAGE_SUBTITLE).value.trim() || '';
            const embeddedPdfPrefix = document.getElementById(DOM_LOCATORS.EMBEDDED_PDF_PREFIX).value.trim() || '';

            const detector = new uiBugsDetector({
                expectedPageSubtitle,
                embeddedPdfPrefix
            });

            const form = await getForm(formVersion);
            const results = await detector.analyze(form);

            this.resultsContent.innerHTML = detector.renderReport(results);
            this.resultsContent.style.display = 'block';

            changeChevronIcon(this.resultsContent);
            copyToClipboardByClass(CSS_CLASSES.COPY_TO_CLIPBOARD);

        } catch (error) {
            this.displayError(error.message);
        } finally {
            this.analyzeBtn.disabled = false;
        }
    }

    displayError(message) {
        this.resultsContent.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="bi ${CSS_CLASSES.EXCLAMATION_TRIANGLE} ${CSS_CLASSES.ME_2}"></i>Error</h4>
                <p class="mb-0">${message}</p>
            </div>
        `;
    }
}

export default UIBugsDetectorManager;
