import {getForm, getMappingVersion} from "../utils/apiRequestHandler";
import CompareMappingWidgetsFormToForm from "../modules/compareMappingWidgetsFormToForm";
import {copyToClipboardByClass, destroyExistingDataTable, parseLogicToAliasesPairsFromMappingItems, toggleFilterOnDataTable} from "../utils/commonHelpers";
import { DOM_LOCATORS, CSS_CLASSES } from '../constants/domLocators';

class CompareMappingWidgetsFormToFormManager {

    constructor(compareBtn, resultsContentId) {
        this.compareBtn = document.getElementById(compareBtn.trim());
        this.resultsContent = document.getElementById(resultsContentId.trim());
    }

    initialize() {
        this.compareBtn.addEventListener('click', this.handleClick.bind(this));
    }

    async handleClick(event) {
        event.preventDefault();

        try {
            this.compareBtn.disabled = true;
            this.resultsContent.style.display = 'block';

            const sourceFormTemplateMappingVersionId = document.getElementById(DOM_LOCATORS.SOURCE_FORM_TEMPLATE_MAPPING_VERSION_ID).value.trim();
            const destinationFormVersionId = document.getElementById(DOM_LOCATORS.DESTINATION_FORM_VERSION_ID).value.trim();

            const mappingVersion = await getMappingVersion(sourceFormTemplateMappingVersionId);
            const {mappingItems} = mappingVersion.content;
            const {lastVersionIdOpt: latestFormMappingVersionId} = mappingVersion.model;
            const {id: currentFormMappingVersionId} = mappingVersion.version;
            const {versionId: currentTemplateVersionId} = mappingVersion.version.mappingVersionTarget.DataTemplateVersionTarget;

            const sourceFormVersionId = this.getSourceFormVersionId(sourceFormTemplateMappingVersionId);
            const sourceForm = await getForm(sourceFormVersionId);
            const {name: sourceFormName, latestVersionId: latestSourceFormVersionId} = sourceForm.formModel;

            const destinationForm = await getForm(destinationFormVersionId);
            const {name: destinationFormName, latestVersionId: latestDestinationFormVersionId} = destinationForm.formModel;

            const headerReportInfo = {
                latestFormMappingVersionId, currentFormMappingVersionId, currentTemplateVersionId,
                sourceFormName, latestSourceFormVersionId, sourceFormVersionId,
                destinationFormName, latestDestinationFormVersionId, destinationFormVersionId,
            };

            const mappingPairs = parseLogicToAliasesPairsFromMappingItems(mappingItems);
            const compareMappingWidgetsFormToForm = new CompareMappingWidgetsFormToForm();
            const results = compareMappingWidgetsFormToForm.generate(sourceForm, destinationForm, mappingPairs);
            console.log(results);

            destroyExistingDataTable(DOM_LOCATORS.COMPARISON_TABLE);
            this.renderResults(results, headerReportInfo);

            toggleFilterOnDataTable(DOM_LOCATORS.COMPARISON_TABLE, DOM_LOCATORS.FILTER_MISMATCH_BTN, 'false');
            copyToClipboardByClass(CSS_CLASSES.COPY_TO_CLIPBOARD);

        } catch (error) {
            this.displayError(error.message);
        } finally {
            this.compareBtn.disabled = false;
        }
    }

    renderResults(results, headerReportInfo) {
        const tableBody = document.getElementById(DOM_LOCATORS.DIFFERENCES_TABLE_BODY);
        tableBody.innerHTML = '';

        const sourceFormVersion = document.getElementById(DOM_LOCATORS.SOURCE_FORM_VERSION);
        sourceFormVersion.innerHTML =
            `<div class="alert alert-info">
                <h5 class="alert-heading">Source Form Name: <strong>${headerReportInfo.sourceFormName}</strong></h5>
                ${headerReportInfo.sourceFormVersionId === headerReportInfo.latestSourceFormVersionId ? '' :
                `<div class="my-1">Latest Form Version ID: <code class="${CSS_CLASSES.COPY_TO_CLIPBOARD}">${headerReportInfo.latestSourceFormVersionId}</code></div>`}
                ${headerReportInfo.currentFormMappingVersionId === headerReportInfo.latestFormMappingVersionId ? '' :
                    `<div class="my-1">Latest Form Mapping Version ID: <code class="${CSS_CLASSES.COPY_TO_CLIPBOARD}">${headerReportInfo.latestFormMappingVersionId}</code></div>`}
            </div>`;

        const destinationFormVersion = document.getElementById(DOM_LOCATORS.DESTINATION_FORM_VERSION);
        destinationFormVersion.innerHTML =
            `<div class="alert alert-info">
                <h5 class="alert-heading">Destination Form Name: <strong>${headerReportInfo.destinationFormName}</strong></h5>
                ${headerReportInfo.destinationFormVersionId === headerReportInfo.latestDestinationFormVersionId ? '' :
                `<div class="my-1">Latest Form Version ID: <code class="${CSS_CLASSES.COPY_TO_CLIPBOARD}">${headerReportInfo.latestDestinationFormVersionId}</code></div>`}
            </div>`;

        let contentHtml = '';

        results.comparisonResults.forEach(result => {
            const sourceTextContent = result.differences.map(difference => {
                if (!difference.areEqual && !difference.onlyInList2) {
                    return `[ ${difference.option} ] | <strong class="${CSS_CLASSES.RED}">${difference.formattedText1}</strong><br>`;
                }
                return '';
            }).join('');

            const destinationTextContent = result.differences.map(difference => {
                if (!difference.areEqual && !difference.onlyInList1) {
                    return `[ ${difference.option} ] | <strong class="${CSS_CLASSES.RED}">${difference.formattedText2}</strong><br>`;
                }
                return '';
            }).join('');

            const hasSourceTextContent = sourceTextContent.length > 0;
            const hasDestinationTextContent = destinationTextContent.length > 0;

            contentHtml += `<tr>
                <!-- Source Form Alias column -->
                <td class="align-top"><strong class="${CSS_CLASSES.PURPLE} ${CSS_CLASSES.COPY_TO_CLIPBOARD}">${result.sourceAlias}</strong>
                    ${result.repeatable1 ? `<strong class="${CSS_CLASSES.RED}">*</strong>${result.repeatable1}` : ''}
                    <br><span class="${CSS_CLASSES.BLUE}">${result.widget1['ui:widget']}</span>${result.widget1['ui:formattedText'] ? `<br>${result.widget1['ui:formattedText']}` : ''}</td>

                <!-- Destination Form Alias column -->
                <td class="align-top"><strong class="${CSS_CLASSES.PURPLE} ${CSS_CLASSES.COPY_TO_CLIPBOARD}">${result.destinationAlias}</strong>
                    ${result.repeatable2 ? `<strong class="${CSS_CLASSES.RED}">*</strong>${result.repeatable2}` : ''}
                    <br><span class="${CSS_CLASSES.BLUE}">${result.widget2['ui:widget']}</span>${result.widget2['ui:formattedText'] ? `<br>${result.widget2['ui:formattedText']}` : ''}</td>

                <!-- Result column -->
                <td class="align-top">${result.areEqual}</td>

                <!-- Source column -->
                <td class="align-top">${result.differences.map(difference => {
                    if (difference.onlyInList1) {
                        return `<strong class="${CSS_CLASSES.RED}">${difference.option}</strong><br>`;
                    } else if (!difference.onlyInList2) {
                        return `${difference.option}<br>`;
                    }
                    return '';
                }).join('')}
                ${hasSourceTextContent ? 
                `<div class="row" id="option-text-1-${result.sourceAlias}" style="display: none">
                    <br>========================<br>
                    ${sourceTextContent}
                </div>` : ''}
                </td>

                <!-- Destination column -->
                <td class="align-top">${result.differences.map(difference => {
                    if (difference.onlyInList2) {
                        return `<strong class="${CSS_CLASSES.RED}">${difference.option}</strong><br>`;
                    } else if (!difference.onlyInList1) {
                        return `${difference.option}<br>`;
                    }
                    return '';
                }).join('')}
                ${hasDestinationTextContent ? 
                `<div class="row" id="option-text-2-${result.sourceAlias}" style="display: none">
                    <br>========================<br>
                    ${destinationTextContent}
                </div>` : ''}
                </td>

                <!-- Add compare text button column -->
                <td class="align-top">
                ${(!result.areEqualText && hasSourceTextContent && hasDestinationTextContent) 
                    ? `<button type="button" class="btn btn-outline-primary btn-sm compare-text-btn" data-alias="${result.sourceAlias}">Compare Text</button>` 
                    : ''}
                </td>
            </tr>`;
        });

        if (tableBody) {
            tableBody.innerHTML = contentHtml;

            const compareTextButtons = document.querySelectorAll('.compare-text-btn');
            compareTextButtons.forEach(button => {
                button.addEventListener('click', this.toggleTextComparison.bind(this));
            });
        }

        if (results.length === 0) {
            const noDataRow = document.createElement('tr');
            const noDataCell = document.createElement('td');
            noDataCell.colSpan = 5;
            noDataCell.textContent = 'No differences found.';
            noDataRow.appendChild(noDataCell);
            tableBody.appendChild(noDataRow);
        }
    }

    displayError = message => {
        this.resultsContent.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="bi ${CSS_CLASSES.EXCLAMATION_TRIANGLE} ${CSS_CLASSES.ME_2}"></i>Error</h4>
                <p class="mb-0">${message}</p>
            </div>
        `;
    };

    toggleTextComparison = event => {
        const alias = event.target.getAttribute('data-alias');
        const textDiv1 = document.getElementById(`option-text-1-${alias}`);
        const textDiv2 = document.getElementById(`option-text-2-${alias}`);

        if (textDiv1) {
            textDiv1.style.display = textDiv1.style.display === 'none' ? 'block' : 'none';
        }

        if (textDiv2) {
            textDiv2.style.display = textDiv2.style.display === 'none' ? 'block' : 'none';
        }
    };

    getSourceFormVersionId = sourceFormTemplateMappingVersionId => {
        if (!sourceFormTemplateMappingVersionId) {
            throw new Error('sourceFormTemplateMappingVersionId is required');
        }

        const idParts = sourceFormTemplateMappingVersionId.split('.');

        if (![3, 4].includes(idParts.length)) {
            throw new Error(
                `Invalid sourceFormTemplateMappingVersionId format: ${sourceFormTemplateMappingVersionId}. Expected 3 or 4 parts separated by dots.`
            );
        }

        return idParts.slice(0, 2).join('.');
    };

    // TODO: Handle a solution that allows the user to copy this method and run it on the console manually for multi-multi mappings
    extractInputOutputPairsForManual = () => {
        const hoverElements = Array.from(document.querySelectorAll(`.${CSS_CLASSES.HOVER_BG_GRAY_1}`));
        const snippetElements = hoverElements.filter(el => {
            const parent = el.parentElement;

            let prevSibling = parent.previousElementSibling;
            while (prevSibling) {
                if (prevSibling.matches(`.${CSS_CLASSES.SPACE_Y_12}`)) return true;
                prevSibling = prevSibling.previousElementSibling;
            }

            return false;
        });

        const inputsOutputs = [];

        snippetElements.forEach(snippetElement => {
            const inputElements = snippetElement.querySelectorAll(`.${CSS_CLASSES.TEXT_GRAY_8} .${CSS_CLASSES.HOVER_UNDERLINE} .${CSS_CLASSES.TRUNCATE}`);
            const aliases1 = Array.from(inputElements).map(element => element.innerText);

            const outputElements = snippetElement.querySelectorAll(`.${CSS_CLASSES.TEXT_GRAY_7} .${CSS_CLASSES.HOVER_UNDERLINE} .${CSS_CLASSES.TRUNCATE}`);
            const aliases2 = Array.from(outputElements).map(element => element.innerText);

            inputsOutputs.push({ aliases1, aliases2 });
        });

        console.log(inputsOutputs);
    };

}

export default CompareMappingWidgetsFormToFormManager;
