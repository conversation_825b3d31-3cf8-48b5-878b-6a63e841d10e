import KkrSubFundCustomIdCheck from "../modules/kkrSubFundCustomIdCheck.js";
import { DOM_LOCATORS } from '../constants/domLocators.js';

class KkrSubFundCustomIdCheckManager {

    constructor(executeBtn, resultsContentId) {
        this.kkrForm = document.getElementById('kkrSubFundCustomIdCheckForm');
        this.executeBtn = document.getElementById(executeBtn);
        this.resultsContent = document.getElementById(resultsContentId);
    }

    initialize() {
        this.executeBtn.addEventListener('click', this.handleClick.bind(this));
    }

    async handleClick(event) {
        event.preventDefault();
        
        const authTokenInput = document.getElementById(DOM_LOCATORS.KKR_AUTHORIZATION_TOKEN);
        const authToken = authTokenInput.value.trim();

        if (!authToken) {
            alert('Please enter an authorization token.');
            return;
        }

        try {
            this.executeBtn.disabled = true;
            this.executeBtn.textContent = 'Checking...';
            this.resultsContent.style.display = 'block';
            
            // Show loading message
            this.showLoadingMessage();

            const kkrChecker = new KkrSubFundCustomIdCheck();
            const results = await kkrChecker.executeCheck(authToken);
            this.updateResults(results);

        } catch (error) {
            console.error('Error during KKR check:', error);
            this.displayError(error.message);
        } finally {
            this.executeBtn.disabled = false;
            this.executeBtn.textContent = 'Execute Check';
        }
    }

    showLoadingMessage() {
        const resultsBody = document.getElementById(DOM_LOCATORS.KKR_SUB_FUND_CUSTOM_ID_CHECK_RESULTS_BODY);
        resultsBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">Checking KKR sub-funds...</div>
                </td>
            </tr>
        `;
    }

    updateResults(results) {
        const resultsBody = document.getElementById(DOM_LOCATORS.KKR_SUB_FUND_CUSTOM_ID_CHECK_RESULTS_BODY);
        resultsBody.innerHTML = '';

        if (results.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 4;
            cell.className = 'text-center';
            cell.textContent = 'No fund data found.';
            row.appendChild(cell);
            resultsBody.appendChild(row);
            return;
        }

        results.forEach(result => {
            const row = document.createElement('tr');

            // Fund Sub ID
            const fundSubIdCell = document.createElement('td');
            fundSubIdCell.textContent = result.fundSubId;
            row.appendChild(fundSubIdCell);

            // Fund Name
            const fundNameCell = document.createElement('td');
            fundNameCell.textContent = result.fundName;
            row.appendChild(fundNameCell);

            // Custom ID
            const customIdCell = document.createElement('td');
            customIdCell.textContent = result.customId;
            row.appendChild(customIdCell);

            // Status
            const statusCell = document.createElement('td');
            statusCell.textContent = result.status;
            
            // Add styling based on status
            if (result.status === 'Has Custom ID') {
                statusCell.className = 'text-success fw-bold';
            } else if (result.status === 'Missing Custom ID') {
                statusCell.className = 'text-warning fw-bold';
            } else if (result.status === 'Error') {
                statusCell.className = 'text-danger fw-bold';
                statusCell.title = result.error || 'Unknown error';
            } else {
                statusCell.className = 'text-muted';
            }
            
            row.appendChild(statusCell);
            resultsBody.appendChild(row);
        });

        // Add summary information
        this.addSummary(results);
    }

    addSummary(results) {
        const hasCustomId = results.filter(r => r.status === 'Has Custom ID').length;
        const missingCustomId = results.filter(r => r.status === 'Missing Custom ID').length;
        const errors = results.filter(r => r.status === 'Error').length;
        const total = results.length;

        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'mt-3 p-3 bg-light rounded';
        summaryDiv.innerHTML = `
            <h6>Summary:</h6>
            <div class="row">
                <div class="col-md-3">
                    <span class="badge bg-primary">Total: ${total}</span>
                </div>
                <div class="col-md-3">
                    <span class="badge bg-success">Has Custom ID: ${hasCustomId}</span>
                </div>
                <div class="col-md-3">
                    <span class="badge bg-warning">Missing Custom ID: ${missingCustomId}</span>
                </div>
                <div class="col-md-3">
                    <span class="badge bg-danger">Errors: ${errors}</span>
                </div>
            </div>
        `;

        // Remove existing summary if present
        const existingSummary = this.resultsContent.querySelector('.summary-section');
        if (existingSummary) {
            existingSummary.remove();
        }

        // Add new summary
        summaryDiv.className += ' summary-section';
        this.resultsContent.querySelector('.card-body').appendChild(summaryDiv);
    }

    displayError(message) {
        const resultsBody = document.getElementById(DOM_LOCATORS.KKR_SUB_FUND_CUSTOM_ID_CHECK_RESULTS_BODY);
        resultsBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading"><i class="bi bi-exclamation-triangle me-2"></i>Error</h6>
                        <p class="mb-0">${message}</p>
                    </div>
                </td>
            </tr>
        `;
    }
}

export default KkrSubFundCustomIdCheckManager;
