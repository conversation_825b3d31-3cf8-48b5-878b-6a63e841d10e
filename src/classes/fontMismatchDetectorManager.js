import FontMismatchDetector from "../modules/fontMismatchDetector";
import {getDocument} from "../utils/apiRequestHandler";
import {copyToClipboardByClass} from "../utils/commonHelpers";
import {DOM_LOCATORS, CSS_CLASSES} from '../constants/domLocators';

class FontMismatchDetectorManager {

    constructor(findMismatchesBtn, resultsContent) {
        this.findMismatchesBtn = document.getElementById(findMismatchesBtn);
        this.resultsContent = document.getElementById(resultsContent);
    }

    initialize() {
        if (this.findMismatchesBtn) {
            this.findMismatchesBtn.addEventListener('click', this.handleClick.bind(this));
        }
    }

    async handleClick(event) {
        event.preventDefault();
        this.findMismatchesBtn.disabled = true;

        try {
            const documentVersion = document.getElementById(DOM_LOCATORS.DOCUMENT_VERSION_ID).value.trim() || '';
            const documentVersionError = document.getElementById(DOM_LOCATORS.DOCUMENT_VERSION_ID_ERROR);
            documentVersionError.textContent = '';

            if (documentVersion.trim() === '') {
                documentVersionError.textContent = 'Please enter a document version!';
                return;
            }

            const expectedFontName = document.getElementById(DOM_LOCATORS.EXPECTED_FONT_NAME).value.trim() || '';
            const expectedFontSize = document.getElementById(DOM_LOCATORS.EXPECTED_FONT_SIZE).value.trim() || '';
            const compareCondition = document.getElementById(DOM_LOCATORS.COMPARE_CONDITION).value.trim() || '';

            const detector = new FontMismatchDetector();
            const res = await getDocument(documentVersion);

            const results = await detector.findMismatches(res, compareCondition, expectedFontName, expectedFontSize);
            detector.renderReport(results);
            this.resultsContent.style.display = 'block';

            copyToClipboardByClass(CSS_CLASSES.COPY_TO_CLIPBOARD);

        } catch (error) {
            this.displayError(error.message);
        } finally {
            this.findMismatchesBtn.disabled = false;
        }
    }

    displayError(message) {
        this.resultsContent.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="bi ${CSS_CLASSES.EXCLAMATION_TRIANGLE} ${CSS_CLASSES.ME_2}"></i>Error</h4>
                <p class="mb-0">${message}</p>
            </div>
        `;
    }
}

export default FontMismatchDetectorManager;
