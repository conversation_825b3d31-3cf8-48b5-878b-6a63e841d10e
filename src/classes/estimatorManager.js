import Estimator from "../modules/estimator";
import {changeChevronIcon, showBoostStrapTooltips} from "../utils/commonHelpers";
import { DOM_LOCATORS } from '../constants/domLocators';
class EstimatorManager {

    constructor(calculateBtn, resultsContentId) {
        this.estimatorForm = document.getElementById('estimatorForm');
        this.calculateBtn = document.getElementById(calculateBtn);
        this.resultsContent = document.getElementById(resultsContentId);
    }

    initialize() {
        changeChevronIcon(this.estimatorForm);
        showBoostStrapTooltips();
        this.calculateBtn.addEventListener('click', this.handleClick.bind(this));
    }

    async handleClick(event) {
        event.preventDefault();
        const fileInput = document.getElementById(DOM_LOCATORS.ESTIMATOR_FILE_INPUT);
        const files = fileInput.files;

        const jsonFilesByPrefix = await this.getJsonFilesByPrefix(files);
        const combinedJsonFiles = this.getCombinedJsonFiles(jsonFilesByPrefix);

        try {
            this.calculateBtn.disabled = true;
            this.resultsContent.style.display = 'block';
            const estimator = new Estimator();
            const predictions = await estimator.calculatePredictions(combinedJsonFiles);
            this.updateResults(predictions);

        } catch (error) {
            this.displayError(error.message);
        } finally {
            this.calculateBtn.disabled = false;
        }
    }

    updateResults(predictions) {
        const resultsBody = document.getElementById(DOM_LOCATORS.ESTIMATOR_RESULTS_BODY);
        resultsBody.innerHTML = '';

        if (predictions.length === 0) {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 6;
            cell.textContent = 'No results available.';
            row.appendChild(cell);
            resultsBody.appendChild(row);
            return;
        }

        predictions.forEach(prediction => {
            const row = document.createElement('tr');

            const fileNameCell = document.createElement('td');
            fileNameCell.textContent = prediction.fileName;
            row.appendChild(fileNameCell);

            const logicPagesCell = document.createElement('td');
            logicPagesCell.textContent = prediction.numberOfLogicPages;
            row.appendChild(logicPagesCell);

            const optionsCell = document.createElement('td');
            optionsCell.textContent = prediction.numberOfOptions;
            row.appendChild(optionsCell);

            const uiBuildTimeCell = document.createElement('td');
            uiBuildTimeCell.textContent = prediction.uiBuildTime;
            row.appendChild(uiBuildTimeCell);

            const logicImplTimeCell = document.createElement('td');
            logicImplTimeCell.textContent = prediction.logicImplTime;
            row.appendChild(logicImplTimeCell);

            const testingTimeCell = document.createElement('td');
            testingTimeCell.textContent = prediction.testingTime;
            row.appendChild(testingTimeCell);

            resultsBody.appendChild(row);
        });
    }

    async getJsonFilesByPrefix(files) {
        if (!files.length) {
            alert('Please select at least one JSON file.');
            return;
        }

        const baseNamePattern = /(.+?)_\d+$/;
        const jsonFilesByPrefix = {};

        for (const file of files) {
            if (file.name.endsWith('.json')) {
                try {
                    const content = await file.text();
                    const data = JSON.parse(content);
                    const match = baseNamePattern.exec(file.name);
                    const baseName = match ? match[1] : file.name.split('.').slice(0, -1).join('.');

                    if (!jsonFilesByPrefix[baseName]) jsonFilesByPrefix[baseName] = [];
                    jsonFilesByPrefix[baseName].push(data);

                } catch (error) {
                    alert(`File ${file.name} is not a valid JSON file.`);
                    return;
                }

            } else {
                alert('Only JSON files are allowed.');
                return;
            }
        }

        return jsonFilesByPrefix;
    }

    getCombinedJsonFiles(jsonFilesByPrefix) {
        if (Object.keys(jsonFilesByPrefix).length === 0) {
            alert('No valid JSON files uploaded.');
            return;
        }

        const combinedJsonFiles = [];

        for (const [baseName, filesData] of Object.entries(jsonFilesByPrefix)) {
            const combinedData = {pdfObjectDTO: []};
            const totalUniquePages = new Set();

            for (const data of filesData) {
                const pdfObjects = data?.pdfObjectDTO || data?.pdfObjects || [];

                if (pdfObjects.length) {
                    combinedData.pdfObjectDTO.push(...pdfObjects);

                    pdfObjects.forEach(obj => {
                        const pageIndex = obj.pageIndex;
                        if (pageIndex !== undefined && pageIndex !== null) {
                            totalUniquePages.add(pageIndex);
                        }
                    });
                }
            }

            combinedData.NumberOfLogicPages = Array.from(totalUniquePages);
            combinedJsonFiles.push([combinedData, baseName]);
        }

        return combinedJsonFiles;
    }

    displayError(message) {
        this.resultsContent.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <h4 class="alert-heading"><i class="bi bi-exclamation-triangle me-2"></i>Error</h4>
                <p class="mb-0">${message}</p>
            </div>
        `;
    }
}

export default EstimatorManager;
