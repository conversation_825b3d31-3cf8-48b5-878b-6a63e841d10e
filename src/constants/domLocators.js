// DOM IDs
export const DOM_LOCATORS = {
    // Sidebar
    SIDEBAR: 'sidebar',
    SIDEBAR_TOGGLE: '.sidebar-toggle',
    BEARER_TOKEN_INPUT: '#bearerTokenInput',
    CONTENT_AREA: 'contentArea',

    // Theme
    THEME_TOGGLE: 'themeToggle',

    // Welcome
    WELCOME_HEADER: 'welcomeHeader',
    WELCOME_CONTENT: 'welcomeContent',

    // Estimator
    ESTIMATOR_CONTENT: 'estimatorContent',
    ESTIMATOR_CARD: 'estimatorCard',
    ESTIMATOR_CALCULATE_BUTTON: 'estimatorCalculateButton',
    ESTIMATOR_RESULTS_CONTENT: 'estimatorResultsContent',
    ESTIMATOR_RESULTS_BODY: 'estimatorResultsBody',
    ESTIMATOR_FILE_INPUT: 'estimatorFileInput',
    FIRST_UI_BUILD_TIME_FACTOR: 'firstUiBuildTimeFactor',
    SECOND_UI_BUILD_TIME_FACTOR: 'secondUiBuildTimeFactor',
    FIRST_LOGIC_IMPLEMENTATION_TIME_FACTOR: 'firstLogicImplementationTimeFactor',
    SECOND_LOGIC_IMPLEMENTATION_TIME_FACTOR: 'secondLogicImplementationTimeFactor',
    FIRST_TESTING_TIME_FACTOR: 'firstTestingTimeFactor',
    SECOND_TESTING_TIME_FACTOR: 'secondTestingTimeFactor',

    // UI Bugs Detector
    UI_BUGS_DETECTOR_CONTENT: 'uiBugsDetectorContent',
    UI_BUGS_DETECTOR_CARD: 'uiBugsDetectorCard',
    UI_BUGS_ANALYZE_BUTTON: 'uiBugsAnalyzeButton',
    UI_BUGS_DETECTOR_RESULTS_CONTENT: 'uiBugsDetectorResultsContent',
    TARGET_FORM_VERSION_ID: 'targetFormVersionId',
    TARGET_FORM_VERSION_ID_ERROR: 'targetFormVersionIdError',
    EXPECTED_PAGE_SUBTITLE: 'expectedPageSubtitle',
    EMBEDDED_PDF_PREFIX: 'embeddedPdfPrefix',

    // Compare Mapping Widgets Form To Form
    COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_CONTENT: 'compareMappingWidgetsFormToFormContent',
    COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_CARD: 'compareMappingWidgetsFormToFormCard',
    COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_COMPARE_BUTTON: 'compareMappingWidgetsFormToFormCompareButton',
    COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_RESULTS_CONTENT: 'compareMappingWidgetsFormToFormResultsContent',
    SOURCE_FORM_TEMPLATE_MAPPING_VERSION_ID: 'sourceFormTemplateMappingVersionId',
    DESTINATION_FORM_VERSION_ID: 'destinationFormVersionId',
    COMPARISON_TABLE: 'comparisonTable',
    FILTER_MISMATCH_BTN: 'filterMismatchBtn',
    DIFFERENCES_TABLE_BODY: 'differencesTableBody',
    SOURCE_FORM_VERSION: 'sourceFormVersion',
    DESTINATION_FORM_VERSION: 'destinationFormVersion',

    // Font Mismatch Detector
    FONT_MISMATCH_DETECTOR_CONTENT: 'fontMismatchDetectorContent',
    FONT_MISMATCH_DETECTOR_CARD: 'fontMismatchDetectorCard',
    FIND_MISMATCHES_BTN: 'findMismatchesBtn',
    FONT_MISMATCH_DETECTOR_RESULTS_CONTENT: 'fontMismatchDetectorResultsContent',
    DOCUMENT_VERSION_ID: 'documentVersionId',
    DOCUMENT_VERSION_ID_ERROR: 'documentVersionIdError',
    EXPECTED_FONT_NAME: 'expectedFontName',
    EXPECTED_FONT_SIZE: 'expectedFontSize',
    COMPARE_CONDITION: 'compareCondition',
    RESULT_MESSAGE: 'resultMessage',
    DOCUMENT_NAME_HEADER: 'documentNameHeader',
    FONT_MISMATCH_DETECTOR_RESULT_TABLE: '#fontMismatchDetectorResultTable tbody',

    // Form to Blueprint
    FORM_TO_BLUEPRINT_CARD: 'formToBlueprintCard',
    FORM_TO_BLUEPRINT_CONTENT: 'formToBlueprintContent',
    FORM_TO_BLUEPRINT_BUTTON: 'formToBlueprintButton',
    FORM_TO_BLUEPRINT_RESULTS_CONTENT: 'formToBlueprintResultsContent',
    RETRO_FORM_VERSION_ID: 'retroFormVersionId',
    BLUEPRINT_ID: 'blueprintId',

    // KKR Sub-fund Custom ID Check
    KKR_SUB_FUND_CUSTOM_ID_CHECK_CARD: 'kkrSubFundCustomIdCheckCard',
    KKR_SUB_FUND_CUSTOM_ID_CHECK_CONTENT: 'kkrSubFundCustomIdCheckContent',
    KKR_SUB_FUND_CUSTOM_ID_CHECK_BUTTON: 'kkrSubFundCustomIdCheckButton',
    KKR_SUB_FUND_CUSTOM_ID_CHECK_RESULTS_CONTENT: 'kkrSubFundCustomIdCheckResultsContent',
    KKR_SUB_FUND_CUSTOM_ID_CHECK_RESULTS_BODY: 'kkrSubFundCustomIdCheckResultsBody',
    KKR_AUTHORIZATION_TOKEN: 'kkrAuthorizationToken',

    // Toast
    TOAST_MSG: 'toastMsg'
};

// CSS Classes
export const CSS_CLASSES = {
    // Theme
    DARK_MODE: 'dark-mode',
    SUN_ICON: 'bi-sun-fill',
    MOON_ICON: 'bi-moon-stars-fill',

    // Custom classes
    COPY_TO_CLIPBOARD: 'copy-to-clipboard',
    PURPLE: 'purple',
    RED: 'red',
    BLUE: 'blue',

    // UI Bugs Detector
    UI_BUGS_GROUP_HEADER: 'ui-bugs-group-header',
    CHEVRON_DOWN: 'bi-chevron-down',
    CHEVRON_UP: 'bi-chevron-up',
    EXCLAMATION_TRIANGLE: 'bi-exclamation-triangle',
    TEXT_MUTED: 'text-muted',
    TEXT_DANGER: 'text-danger',
    BG_DANGER: 'bg-danger',
    ME_2: 'me-2',
    HOVER_BG_GRAY_1: 'hover\\:bg-gray-1',
    HOVER_UNDERLINE: 'hover\\:underline',
    TRUNCATE: 'truncate',
    SPACE_Y_12: 'space-y-12',

    // Content render
    CONTENT_RENDER: 'content-render'
}; 