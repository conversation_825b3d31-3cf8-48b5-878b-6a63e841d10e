export const WIDGET_TYPES = {
    HEADER: "Header",
    TEXTBOX: "TextBox",
    TEXTAREA: "TextArea",
    PARAGRAPH: "Paragraph",
    FILE_GROUP: "FileGroup",
    REPEATABLE: "Repeatable",
    RADIO: "Radio",
    MULTIPLE_CHECKBOX: "MultipleCheckbox",
    DROPDOWN: "Dropdown",
    COUNTRY: "Country",
    STATE: "State",
    SIGNATURE: "Signature",
};

export const WIDGET_PROPERTIES = {
    UI_WIDGET: "ui:widget",
    UI_FORMATTED_TEXT: "ui:formattedText",
    UI_INVISIBLE: "ui:invisible",
    UI_PDF_MAPPING: "ui:pdfMapping",
    UI_HEADER_TYPE: "ui:headerType",
    UI_SUBTITLE: "ui:subtitle",
    UI_SUPPORTING_FILE_GROUP: "ui:supportingFileGroup",
    UI_MULTIPLE_OPTION: "ui:multipleOption",
    UI_MAX_LENGTH: "ui:maxLength",
    UI_REQUIRED: "ui:required",
    UI_RECOMMENDED: "ui:recommended",
    UI_TOOLTIP_FOR_DISABLED_STATE: "ui:tooltipForDisabledState",
    UI_TITLE: "ui:title",
    UI_WELL_TYPE: "ui:wellType",
    UI_INDENTATION: "ui:indentation",
    UI_HEADING: "ui:heading",
    UI_PLACEHOLDER: "ui:placeholder",
    UI_DESCRIPTION: "ui:description",
    UI_SIGNATURE_TYPE: "ui:signatureType",
    UI_SIGNATURE_SIGNER: "ui:signatureSigner",
    UI_UNIQUE_SIGNER_EMAIL: "ui:uniqueSignerEmail",
    UI_SIGNATURE: "ui:signature",
}

export const OPTION_PROPERTIES = {
    FORMATTED_TEXT: "formattedText",
    PDF_MAPPINGS: "pdfMappings",
    FIELDS_BELOW: "fieldsBelow",
    BLUEPRINT_METADATA_MAPPING: "blueprintMetadataMapping",
}
