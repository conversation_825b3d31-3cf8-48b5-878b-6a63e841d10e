import lodash from 'lodash';
import {WIDGET_TYPES, WIDGET_PROPERTIES, OPTION_PROPERTIES} from '../constants/formWidgetConstants';

export default class CompareMappingWidgetsFormToForm {

    constructor() {
    }

    generate(sourceForm, destinationForm, mappingPairs) {
        const {uiSchema: sourceUiSchema} = sourceForm.formData.form.namespaceFormSchemaMap.main;
        const {schema: sourceSchema} = sourceForm.formData.form.namespaceFormSchemaMap.main;

        const {uiSchema: destinationUiSchema} = destinationForm.formData.form.namespaceFormSchemaMap.main;
        const {schema: destinationSchema} = destinationForm.formData.form.namespaceFormSchemaMap.main;

        let sourceWidgets = [];
        let destWidgets = [];

        const sourceAliases = mappingPairs.map(pair => pair.alias1).filter(Boolean);
        const destAliases = mappingPairs.map(pair => pair.alias1).filter(Boolean); // intentionally get form alias: alias1

        for (let key of sourceAliases) {
            if (sourceUiSchema.hasOwnProperty(key)) {
                sourceWidgets.push({alias: key, widget: sourceUiSchema[key]});
            }
        }

        for (let key of destAliases) {
            if (destinationUiSchema.hasOwnProperty(key)) {
                destWidgets.push({alias: key, widget: destinationUiSchema[key]});
            }
        }

        // Compare the widgets
        const differences = this.compareOptionValues(sourceWidgets, sourceSchema, destWidgets, destinationSchema, mappingPairs);

        // Process repeatable pairs if they exist
        if (differences.repeatablePairs && differences.repeatablePairs.length > 0) {
            for (const {aliases1, aliases2, repeatable1, repeatable2} of differences.repeatablePairs) {
                const repAliasPairs = this.extractAliasesPair(new Set(aliases1), new Set(aliases2));
                const diff = this.compareOptionValues(aliases1, sourceSchema, aliases2, destinationSchema, repAliasPairs);

                for (const result of diff.comparisonResults) {
                    result.repeatable1 = repeatable1 ?? '';
                    result.repeatable2 = repeatable2 ?? '';
                }

                differences.comparisonResults = differences.comparisonResults.concat(diff.comparisonResults);
                differences.onlyInList1 = differences.onlyInList1.concat(diff.onlyInList1);
                differences.onlyInList2 = differences.onlyInList2.concat(diff.onlyInList2);
            }
        }

        differences.comparisonResults = this.filterUniqueDifferences(differences.comparisonResults);
        return differences;
    }

    /**
     * Extract pairs of aliases from two sets
     */
    extractAliasesPair(aliases1, aliases2) {
        // Convert to arrays if they're not already sets
        if (!(aliases1 instanceof Set)) aliases1 = new Set(aliases1);
        if (!(aliases2 instanceof Set)) aliases2 = new Set(aliases2);

        const allAliases = new Set([...aliases1, ...aliases2]);
        const aliasPairs = [];

        allAliases.forEach(alias => {
            if (aliases1.has(alias) && aliases2.has(alias)) {
                aliasPairs.push({alias1: alias, alias2: alias});

            } else if (aliases1.has(alias)) {
                aliasPairs.push({alias1: alias, alias2: null});

            } else if (aliases2.has(alias)) {
                aliasPairs.push({alias1: null, alias2: alias});
            }
        });

        return aliasPairs;
    }

    /**
     * Compare two lists and identify differences
     */
    compareLists(list1, list2) {
        const set1 = new Set(list1);
        const set2 = new Set(list2);
        const allItems = new Set([...list1, ...list2]);
        const itemInfo = [];

        allItems.forEach(option => {
            if (set1.has(option) && set2.has(option)) {
                itemInfo.push({option, areEqual: true});

            } else if (set1.has(option)) {
                itemInfo.push({option, onlyInList1: true, areEqual: true});

            } else if (set2.has(option)) {
                itemInfo.push({option, onlyInList2: true, areEqual: true});
            }
        });

        return itemInfo;
    }

    /**
     * Compare options from two maps
     */
    compareOptions(optionsMap1, optionsMap2) {
        const itemInfoList = [];
        const allKeys = new Set([...optionsMap1.keys(), ...optionsMap2.keys()]);

        allKeys.forEach(key => {
            const option1 = optionsMap1.get(key);
            const option2 = optionsMap2.get(key);

            const formattedText1 = option1 ?
                (lodash.get(option1, OPTION_PROPERTIES.FORMATTED_TEXT) || '').replace(/FOOT_NOTE_HERE/g, '').trim() : '';
            const formattedText2 = option2 ?
                (lodash.get(option2, OPTION_PROPERTIES.FORMATTED_TEXT) || '').replace(/FOOT_NOTE_HERE/g, '').trim() : '';

            if (option1 && option2) {
                const areEqual = formattedText1 === formattedText2;
                itemInfoList.push({
                    option: key,
                    formattedText1,
                    formattedText2,
                    areEqual
                });

            } else if (option1 && !option2) {
                itemInfoList.push({
                    option: key,
                    formattedText1,
                    formattedText2: "",
                    areEqual: true,
                    onlyInList1: true
                });

            } else if (!option1 && option2) {
                itemInfoList.push({
                    option: key,
                    formattedText1: "",
                    formattedText2,
                    areEqual: true,
                    onlyInList2: true
                });
            }
        });

        return itemInfoList;
    }

    getAllPropertyNames(alias, schema) {
        const result = [];
        let isCollecting = false;

        function recursiveExtract(properties, collect = false) {
            for (const property of properties) {
                if (property.name === alias) {
                    collect = true;
                    isCollecting = true;
                }

                if (collect) {
                    if (property.name && property.name !== alias && !["object", "null"].includes(property.type)) {
                        result.push(property.name);
                    }
                }

                if ("properties" in property && property.properties) {
                    recursiveExtract(property.properties, isCollecting);
                }

                if (property.items?.properties) {
                    recursiveExtract(property.items.properties, isCollecting);
                }

                if (property.name === alias) {
                    isCollecting = false;
                }
            }
        }

        if (schema.type === "object" && schema.properties) {
            recursiveExtract([schema], false);
        }

        return result;
    }

    /**
     * Compare option values between widgets
     */
    compareOptionValues(uiSchemaList1, schema1, uiSchemaList2, schema2, aliasPairs) {
        const widgetMap1 = new Map();
        const widgetMap2 = new Map();

        // Create maps for easier lookup
        uiSchemaList1.forEach(item => widgetMap1.set(item.alias, item.widget));
        uiSchemaList2.forEach(item => widgetMap2.set(item.alias, item.widget));

        const comparisonResults = [];
        const onlyInList1 = [];
        const onlyInList2 = [];
        const repeatablePairs = [];

        /**
         * const {alias1: sourceAlias, alias1: destinationAlias} of aliasPairs
         * alias1 and alias2 are intentionally the same
         * to maintain the original solution, maybe can reuse this for the new tool
         */
        for (const {alias1: sourceAlias, alias1: destinationAlias} of aliasPairs) {
            const widget1 = sourceAlias ? widgetMap1.get(sourceAlias) : null;
            const widget2 = destinationAlias ? widgetMap2.get(destinationAlias) : null;

            if (!widget1) {
                if (destinationAlias != null) {
                    onlyInList2.push(destinationAlias);
                }
                continue;
            }

            if (!widget2) {
                if (sourceAlias != null) {
                    onlyInList1.push(sourceAlias);
                }
                continue;
            }

            const widgetType1 = lodash.get(widget1, WIDGET_PROPERTIES.UI_WIDGET) || '';
            const widgetType2 = lodash.get(widget2, WIDGET_PROPERTIES.UI_WIDGET) || '';

            let childItems1 = [];
            let childItems2 = [];
            let differences = [];

            if (widgetType1 === WIDGET_TYPES.REPEATABLE && widgetType2 === WIDGET_TYPES.REPEATABLE) {
                childItems1 = this.getAllPropertyNames(sourceAlias, schema1);
                childItems2 = this.getAllPropertyNames(destinationAlias, schema2);
                repeatablePairs.push({"repeatable1": sourceAlias, "aliases1": childItems1, "repeatable2": destinationAlias, "aliases2": childItems2});
                differences = this.compareLists(childItems1, childItems2);

            } else {
                childItems1 = widgetType1 === WIDGET_TYPES.DROPDOWN && (widgetType2 === WIDGET_TYPES.COUNTRY || widgetType2 === WIDGET_TYPES.STATE) ? [] :
                    lodash.get(widget1, `${WIDGET_PROPERTIES.UI_MULTIPLE_OPTION}.options`, []);
                childItems2 = widgetType2 === WIDGET_TYPES.DROPDOWN && (widgetType1 === WIDGET_TYPES.COUNTRY || widgetType1 === WIDGET_TYPES.STATE) ? [] :
                    lodash.get(widget2, `${WIDGET_PROPERTIES.UI_MULTIPLE_OPTION}.options`, []);

                const mapItem1 = new Map(childItems1.reduce((acc, [key, value]) => {
                    acc.push([key, value]);
                    return acc;
                }, []));

                const mapItem2 = new Map(childItems2.reduce((acc, [key, value]) => {
                    acc.push([key, value]);
                    return acc;
                }, []));

                if (childItems1.length > 0 && childItems2.length > 0) {
                    differences = this.compareOptions(mapItem1, mapItem2);
                }
            }

            // Determine if the parents and all options (if any) are equal
            const areEqual = (widgetType1 === widgetType2) &&
                differences.every(item => !item.onlyInList1 && !item.onlyInList2 && item.areEqual);

            const areEqualText = differences.every(item => item.formattedText1 === item.formattedText2);

            comparisonResults.push({
                sourceAlias,
                widget1,
                destinationAlias,
                widget2,
                areEqual,
                areEqualText,
                differences
            });
        }

        return {
            comparisonResults,
            onlyInList1,
            onlyInList2,
            repeatablePairs
        };
    }

    filterUniqueDifferences(object) {
        const uniqueDifferences = [];
        const uniqueOptions = new Set();

        for (const result of object) {
            const resultKey = JSON.stringify(result);

            if (!uniqueOptions.has(resultKey)) {
                uniqueDifferences.push(result);
                uniqueOptions.add(resultKey);
            }
        }

        return uniqueDifferences;
    }
}
