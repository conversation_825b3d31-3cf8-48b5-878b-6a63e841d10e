import { DOM_LOCATORS, CSS_CLASSES } from '../constants/domLocators';
import { LOCAL_STORAGE_KEYS } from '../constants/localStorageKeys';
import {getValueFromLocalStorage, setValueToLocalStorage} from "../utils/localStorageHandler";

export function initializeThemeToggle() {
    const themeToggle = document.getElementById(DOM_LOCATORS.THEME_TOGGLE);
    const isDarkMode = getValueFromLocalStorage(LOCAL_STORAGE_KEYS.DARK_MODE) === 'enabled';

    if (isDarkMode) {
        document.body.classList.add(CSS_CLASSES.DARK_MODE);
        updateToggleButton(true);
    }

    themeToggle.addEventListener('click', () => {
        document.body.classList.toggle(CSS_CLASSES.DARK_MODE);
        const isDarkMode = document.body.classList.contains(CSS_CLASSES.DARK_MODE);
        setValueToLocalStorage(LOCAL_STORAGE_KEYS.DARK_MODE, isDarkMode ? 'enabled' : 'disabled');
        updateToggleButton(isDarkMode);
    });

    function updateToggleButton(isDarkMode) {
        const icon = themeToggle.querySelector('i');
        icon.classList.remove(isDarkMode ? CSS_CLASSES.SUN_ICON : CSS_CLASSES.MOON_ICON);
        icon.classList.add(isDarkMode ? CSS_CLASSES.MOON_ICON : CSS_CLASSES.SUN_ICON);
    }
}
