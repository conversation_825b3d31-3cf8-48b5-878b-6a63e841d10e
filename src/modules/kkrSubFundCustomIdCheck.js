export default class KkrSubFundCustomIdCheck {
    constructor() {
        this.entityId = "ent3gwnvvye20xo1"; // Hardcoded entity ID as per requirements
    }

    /**
     * Executes the KKR sub-fund custom ID check process
     * @param {string} authToken - Authorization token for API calls
     * @returns {Promise<Array>} - Array of fund check results
     */
    async executeCheck(authToken) {
        try {
            // Step 1: Query entity data to get fund sub IDs
            const entityData = await this.queryEntityId(authToken);
            
            if (!entityData || !entityData.entityRestrictedModel || !entityData.entityRestrictedModel.fundSubIds) {
                throw new Error('Unable to retrieve fund sub IDs from entity data');
            }

            const fundSubIds = entityData.entityRestrictedModel.fundSubIds;
            console.log(`Found ${fundSubIds.length} fund sub IDs:`, fundSubIds);

            // Step 2: Check each fund sub ID for custom ID
            const results = [];
            for (const fundSubId of fundSubIds) {
                try {
                    const fundData = await this.getFundSubPortalData(authToken, fundSubId);
                    const fundResult = this.processFundData(fundSubId, fundData);
                    results.push(...fundResult);
                } catch (error) {
                    console.error(`Error processing fund sub ID ${fundSubId}:`, error);
                    results.push({
                        fundSubId: fundSubId,
                        fundName: 'Error retrieving data',
                        customId: 'N/A',
                        status: 'Error',
                        error: error.message
                    });
                }
            }

            return results;
        } catch (error) {
            console.error('Error in executeCheck:', error);
            throw error;
        }
    }

    /**
     * Calls the queryEntityId API
     * @param {string} authToken - Authorization token
     * @returns {Promise<Object>} - API response
     */
    async queryEntityId(authToken) {
        const url = 'https://portal.anduin.app/api/v3/portal/queryEntityId';
        const headers = {
            'authorization': `Bearer ${authToken}`,
            'priority': 'u=1, i',
            'sec-gpc': '1',
            'x-anduin-request-id': this.generateRequestId(),
            'x-anduin-tab-id': this.generateTabId(),
            'content-type': 'application/json'
        };

        const body = JSON.stringify({
            entityId: this.entityId
        });

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: body
        });

        if (!response.ok) {
            throw new Error(`Query entity API failed: ${response.status} - ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Calls the getFundSubPortalData API
     * @param {string} authToken - Authorization token
     * @param {string} fundSubId - Fund sub ID to query
     * @returns {Promise<Object>} - API response
     */
    async getFundSubPortalData(authToken, fundSubId) {
        const url = 'https://portal.anduin.app/api/v3/fundSubOperation/getFundSubPortalData';
        const headers = {
            'authorization': `Bearer ${authToken}`,
            'priority': 'u=1, i',
            'sec-gpc': '1',
            'x-anduin-request-id': this.generateRequestId(),
            'x-anduin-tab-id': this.generateTabId(),
            'content-type': 'application/json'
        };

        const body = JSON.stringify(fundSubId);

        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: body
        });

        if (!response.ok) {
            throw new Error(`Get fund sub portal data API failed: ${response.status} - ${response.statusText}`);
        }

        return await response.json();
    }

    /**
     * Processes fund data to extract investment funds and check for custom IDs
     * @param {string} fundSubId - Fund sub ID
     * @param {Object} fundData - Fund data from API
     * @returns {Array} - Array of fund check results
     */
    processFundData(fundSubId, fundData) {
        const results = [];

        if (!fundData || !fundData.fundSubGeneralInfo || !fundData.fundSubGeneralInfo.investmentFunds) {
            return [{
                fundSubId: fundSubId,
                fundName: 'No investment funds data',
                customId: 'N/A',
                status: 'No Data'
            }];
        }

        const investmentFunds = fundData.fundSubGeneralInfo.investmentFunds;
        
        investmentFunds.forEach((fund, index) => {
            const hasCustomId = fund.hasOwnProperty('customId') && fund.customId !== null && fund.customId !== undefined;
            const customIdValue = hasCustomId ? fund.customId : 'Not Present';
            const status = hasCustomId ? 'Has Custom ID' : 'Missing Custom ID';
            
            results.push({
                fundSubId: fundSubId,
                fundName: fund.name || `Fund ${index + 1}`,
                customId: customIdValue,
                status: status
            });
        });

        return results;
    }

    /**
     * Generates a random request ID
     * @returns {string} - Random UUID-like string
     */
    generateRequestId() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    /**
     * Generates a random tab ID
     * @returns {string} - Random UUID-like string
     */
    generateTabId() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}
