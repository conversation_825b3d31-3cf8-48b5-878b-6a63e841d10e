import { DOM_LOCATORS } from '../constants/domLocators';

export default class Estimator {
    constructor() {
    }

    async calculatePredictions(jsonFiles) {
        const results = {};
        const dfData = [];
        const baseNamePattern = /(.+?)_\d+$/;

        jsonFiles.forEach(([data, fileName]) => {
            console.debug(`Processing file: ${fileName}`);

            const match = baseNamePattern.exec(fileName);
            const baseName = match ? match[1] : fileName;

            const pdfObjects = data.pdfObjectDTO || data.pdfObjects;
            if (!pdfObjects) {
                console.warn(`No pdfObjects found in file: ${fileName}, skipping.`);
                return;
            }

            let textFieldCount = 0;
            let radioFieldCount = 0;
            let signatureFieldCount = 0;
            let checkboxFieldCount = 0;
            const uniquePageIndicesInFile = new Set();

            pdfObjects.forEach(obj => {
                let pageIndex;
                if (obj.PdfTextField || obj.__typename__ === "PdfTextFieldDTO") {
                    textFieldCount += 1;
                    pageIndex = obj.pageIndex || (obj.PdfTextField && obj.PdfTextField.pageIndex);
                } else if (obj.PdfRadioField || obj.__typename__ === "PdfRadioFieldDTO") {
                    radioFieldCount += 1;
                    pageIndex = obj.pageIndex || (obj.PdfRadioField && obj.PdfRadioField.pageIndex);
                } else if (obj.PdfSignatureField || obj.__typename__ === "PdfSignatureFieldDTO") {
                    signatureFieldCount += 1;
                    pageIndex = obj.pageIndex || (obj.PdfSignatureField && obj.PdfSignatureField.pageIndex);
                } else if (obj.PdfCheckboxField || obj.__typename__ === "PdfCheckboxFieldDTO") {
                    checkboxFieldCount += 1;
                    pageIndex = obj.pageIndex || (obj.PdfCheckboxField && obj.PdfCheckboxField.pageIndex);
                } else {
                    return;
                }

                if (pageIndex !== undefined && pageIndex !== null) {
                    uniquePageIndicesInFile.add(pageIndex);
                }
            });

            const numberOfOptions = radioFieldCount + checkboxFieldCount;

            if (results[baseName]) {
                results[baseName].PdfTextField += textFieldCount;
                results[baseName].PdfRadioField += radioFieldCount;
                results[baseName].PdfSignatureField += signatureFieldCount;
                results[baseName].PdfCheckboxField += checkboxFieldCount;
                results[baseName].NumberOfLogicPages += uniquePageIndicesInFile.size;
                results[baseName].NumberOfOptions += numberOfOptions;

            } else {
                results[baseName] = {
                    PdfTextField: textFieldCount,
                    PdfRadioField: radioFieldCount,
                    PdfSignatureField: signatureFieldCount,
                    PdfCheckboxField: checkboxFieldCount,
                    NumberOfLogicPages: uniquePageIndicesInFile.size,
                    NumberOfOptions: numberOfOptions
                };
            }
        });

        for (const [baseName, counts] of Object.entries(results)) {
            const firstUiBuildTimeFactor = parseFloat(document.getElementById(DOM_LOCATORS.FIRST_UI_BUILD_TIME_FACTOR).value) || 0;
            const secondUiBuildTimeFactor = parseFloat(document.getElementById(DOM_LOCATORS.SECOND_UI_BUILD_TIME_FACTOR).value) || 0;
            const firstLogicImplTimeFactor = parseFloat(document.getElementById(DOM_LOCATORS.FIRST_LOGIC_IMPLEMENTATION_TIME_FACTOR).value) || 0;
            const secondLogicImplTimeFactor = parseFloat(document.getElementById(DOM_LOCATORS.SECOND_LOGIC_IMPLEMENTATION_TIME_FACTOR).value) || 0;
            const firstTestingTimeFactor = parseFloat(document.getElementById(DOM_LOCATORS.FIRST_TESTING_TIME_FACTOR).value) || 0;
            const secondTestingTimeFactor = parseFloat(document.getElementById(DOM_LOCATORS.SECOND_TESTING_TIME_FACTOR).value) || 0;

            const uiBuildTime = Math.max(
                Math.round(
                    ((firstUiBuildTimeFactor * counts.NumberOfLogicPages + secondUiBuildTimeFactor * counts.NumberOfOptions) * 0.85) * 10
                ) / 10,
                0.5
            );

            const logicImplTime = Math.max(
                Math.round(
                    ((firstLogicImplTimeFactor * counts.NumberOfLogicPages + secondLogicImplTimeFactor * counts.NumberOfOptions) * 0.8) * 10
                ) / 10,
                0.5
            );

            const testingTime = Math.max(
                Math.round(
                    ((firstTestingTimeFactor * counts.NumberOfLogicPages + secondTestingTimeFactor * counts.NumberOfOptions) * 0.85) * 10
                ) / 10,
                0.5
            );

            dfData.push({
                fileName: baseName,
                numberOfLogicPages: counts.NumberOfLogicPages,
                numberOfOptions: counts.NumberOfOptions,
                uiBuildTime: uiBuildTime,
                logicImplTime: logicImplTime,
                testingTime: testingTime
            });
        }

        return dfData;
    }
}
