import {
    createBlueprintVersion,
    createOrEditBlueprintSignature,
    createOrEditFileGroup,
    createOrEditGatingQuestion,
    createOrEditWell
} from "../utils/blueprintApiRequestHandler";
import {WIDGET_PROPERTIES, WIDGET_TYPES} from "../constants/formWidgetConstants";
import { findPropertyNamesUnderObject } from "../utils/commonHelpers";

/**
 * Factory for creating widget processors
 */
class WidgetProcessorFactory {
    /**
     * Creates a processor for the specified widget type
     * @param {string} widgetType - Type of widget to process
     * @param {FormToBlueprint} context - FormToBlueprint instance for context
     * @returns {WidgetProcessor} - Appropriate processor for the widget type
     */
    static createProcessor(widgetType, context) {
        const processors = {
            'FILE_GROUP': new FileGroupProcessor(context),
            'SIGNATURE': new SignatureProcessor(context),
            'GATING_QUESTION': new GatingQuestionProcessor(context),
            'WELL': new WellProcessor(context)
        };
        return processors[widgetType] || new NullProcessor();
    }
}

/**
 * Base class for widget processors
 */
class WidgetProcessor {
    /**
     * @param {FormToBlueprint} context - FormToBlueprint instance
     */
    constructor(context) {
        this.context = context;
        this.widgetType = '';
    }

    /**
     * Finds widgets in the schema
     * @param {Object} uiSchema - UI Schema to search
     * @returns {Array} - Found widgets
     */
    findWidgets(mainSchema) {
        return [];
    }

    /**
     * Transforms widget data to payload
     * @param {Object} widgetData - Widget data
     * @returns {Object} - Payload for API
     */
    transformToPayload(widgetData) {
        return {};
    }

    /**
     * Processes all widgets of a type
     * @param {Object} uiSchema - UI Schema containing widgets
     * @returns {Promise<void>}
     */
    async process(mainSchema) {
        throw new Error('Method not implemented');
    }

    /**
     * Generates a session ID
     * @returns {string} - Session ID
     */
    generateSessionId() {
        return `blp-${this.context.generateRandomId(8)}-${this.context.generateRandomId(4)}-${this.context.generateRandomId(4)}-${this.context.generateRandomId(4)}-${this.context.generateRandomId(12)}`;
    }
}

/**
 * Processor for FileGroup widgets
 */
class FileGroupProcessor extends WidgetProcessor {
    constructor(context) {
        super(context);
        this.widgetType = WIDGET_TYPES.FILE_GROUP;
    }

    findWidgets(mainSchema) {
        const { uiSchema, schema } = mainSchema;
        // If aliases array is empty, get all widgets of this type
        if (!this.context.aliases || this.context.aliases.length === 0) {
            return this.context.findWidgetsByType(uiSchema, this.widgetType);
        }

        // Otherwise, filter for widgets that match both the type and one of the aliases
        const results = [];
        this.context.aliases.forEach(alias => {
            const widget = this.context.findWidgetByKey(uiSchema, alias);
            if(widget) {
                const childAliases = findPropertyNamesUnderObject(schema, alias, 'array');
                childAliases.forEach(childAlias => {
                    const childWidget = this.context.findWidgetByKey(uiSchema, childAlias);
                    if (childWidget && childWidget.object && childWidget.object[WIDGET_PROPERTIES.UI_WIDGET] === this.widgetType) {
                        console.debug(`Found ${this.widgetType} with alias: ${childAlias}`);
                        results.push(childWidget);
                    }
                });
                if (widget.object && widget.object[WIDGET_PROPERTIES.UI_WIDGET] === this.widgetType) {
                    console.debug(`Found ${this.widgetType} with alias: ${alias}`);
                    results.push(widget);
                }
            }
        });

        return results;
    }

    transformToPayload(fileGroupData) {
        const { path, object: obj } = fileGroupData;
        const supportingFileGroup = obj[WIDGET_PROPERTIES.UI_SUPPORTING_FILE_GROUP] || {};
        const files = supportingFileGroup.files || {};

        // Transform file items
        const fileItems = Object.entries(files).map(([key, value]) => ({
            id: this.context.generateRandomId(),
            key: this.context.ensureValidKey(key),
            name: value.description || "",
            helpText: value.helpText || ""
        }));

        // Generate random ID for the fileGroup
        const fileGroupId = this.context.generateRandomId();
        const uniqueKey = this.context.ensureUniqueKey(path);

        // Create the payload
        const payload = {
            blueprintId: this.context.blueprintId,
            fileGroupIdOpt: null,
            fileGroup: {
                id: fileGroupId,
                key: this.context.ensureValidKey(uniqueKey),
                name: supportingFileGroup.description || "",
                helpText: supportingFileGroup.helpText || "",
                fileItems
            },
            sessionId: this.generateSessionId()
        };

        this.context.addTransferredField(path, uniqueKey);
        return payload;
    }

    async process(mainSchema) {
        try {
            const widgets = this.findWidgets(mainSchema);
            for (const widget of widgets) {
                await createOrEditFileGroup(this.transformToPayload(widget));
            }

            console.debug(`All ${this.widgetType} widgets processed successfully.`);
            return widgets.length;
        } catch (error) {
            console.error(`Error processing ${this.widgetType} widgets:`, error);
            throw error;
        }
    }
}

/**
 * Processor for Signature widgets
 */
class SignatureProcessor extends WidgetProcessor {
    constructor(context) {
        super(context);
        this.widgetType = WIDGET_TYPES.SIGNATURE;
    }

    findWidgets(mainSchema) {
        const { uiSchema, schema } = mainSchema;
        // If aliases array is empty, get all widgets of this type
        if (!this.context.aliases || this.context.aliases.length === 0) {
            return this.context.findWidgetsByType(uiSchema, this.widgetType);
        }

        // Otherwise, filter for widgets that match both the type and one of the aliases
        const results = [];
        this.context.aliases.forEach(alias => {
            const widget = this.context.findWidgetByKey(uiSchema, alias);
            if(widget) {
                const childAliases = findPropertyNamesUnderObject(schema, alias, 'null');
                childAliases.forEach(childAlias => {
                    const childWidget = this.context.findWidgetByKey(uiSchema, childAlias);
                    if (childWidget && childWidget.object && childWidget.object[WIDGET_PROPERTIES.UI_WIDGET] === this.widgetType) {
                        results.push(childWidget);
                    }
                });
                if (widget.object && widget.object[WIDGET_PROPERTIES.UI_WIDGET] === this.widgetType) {
                    results.push(widget);
                }
            }
        });

        return results;
    }

    transformToPayload(signatureData) {
        const { path, object: obj } = signatureData;

        // Extract signature type and signer
        const signatureType = obj[WIDGET_PROPERTIES.UI_SIGNATURE_TYPE] || "LpSignature";
        const signer = obj[WIDGET_PROPERTIES.UI_SIGNATURE_SIGNER] || "";
        const uniqueSignerEmail = obj[WIDGET_PROPERTIES.UI_UNIQUE_SIGNER_EMAIL] || false;

        // Extract and transform mappings
        const mappings = Array.isArray(obj[WIDGET_PROPERTIES.UI_SIGNATURE])
            ? obj[WIDGET_PROPERTIES.UI_SIGNATURE].map(item => ({
                mapping: item.mapping,
                signatureType: item.signatureType,
                tooltip: item.tooltip || "",
                isOptional: item.isOptional || false,
                ...(item.customTypeConfig && {customTypeConfig: item.customTypeConfig})
            }))
            : [];

        // Generate random ID for the signature
        const signatureId = this.context.generateRandomId();
        const uniqueKey = this.context.ensureUniqueKey(path);

        // Create the payload
        const payload = {
            blueprintId: this.context.blueprintId,
            signatureIdOpt: null,
            signature: {
                id: signatureId,
                key: this.context.ensureValidKey(uniqueKey),
                signatureType,
                signer,
                mappings,
                uniqueSignerEmail
            },
            sessionId: this.generateSessionId()
        };

        this.context.addTransferredField(path, uniqueKey);
        return payload;
    }

    async process(mainSchema) {
        try {
            const widgets = this.findWidgets(mainSchema);
            for (const widget of widgets) {
                await createOrEditBlueprintSignature(this.transformToPayload(widget));
            }
            return widgets.length;
        } catch (error) {
            console.error(`Error processing ${this.widgetType} widgets:`, error);
            throw error;
        }
    }
}

/**
 * Processor for GatingQuestion widgets
 */
class GatingQuestionProcessor extends WidgetProcessor {
    constructor(context) {
        super(context);
        this.widgetType = 'GatingQuestion';
    }

    findWidgets(mainSchema) {
        try {
            const { uiSchema, schema } = mainSchema;
            // GatingQuestion requires aliases to identify the correct fields
            if (!this.context.aliases || this.context.aliases.length === 0) {
                console.warn('No aliases provided for GatingQuestion processing. Specify aliases to identify questions.');
                return [];
            }

            const gatingQuestionWidgets = [];

            this.context.aliases.forEach(alias => {
                const widget = this.context.findWidgetByKey(uiSchema, alias);
                if (widget) {
                    const childAliases = findPropertyNamesUnderObject(schema, alias);
                    childAliases.forEach(childAlias => {
                        const childWidget = this.context.findWidgetByKey(uiSchema, childAlias);
                        if (childWidget &&
                            (childWidget.object[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.RADIO ||
                                childWidget.object[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.MULTIPLE_CHECKBOX ||
                                childWidget.object[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.DROPDOWN)) {
                            gatingQuestionWidgets.push(childWidget);
                        }
                    });
                    if (widget.object && (widget.object[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.RADIO ||
                        widget.object[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.MULTIPLE_CHECKBOX ||
                        widget.object[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.DROPDOWN)) {
                        results.push(widget);
                    }
                }
            });
            return gatingQuestionWidgets;
        } catch (error) {
            console.error('Error finding Gating Question widgets:', error);
            return [];
        }
    }

    transformToPayload(multipleOptionData) {
        const { path, object: obj } = multipleOptionData;
        const widgetType = obj[WIDGET_PROPERTIES.UI_WIDGET];

        // Determine input type based on widget type
        let inputType = {};
        if (['Radio', 'MultipleCheckbox', 'Dropdown'].includes(widgetType)) {
            inputType = {
                [widgetType]: {}
            };
        }

        // Map options
        const options = obj[WIDGET_PROPERTIES.UI_MULTIPLE_OPTION]?.['options']?.map(option => {
            const [key, value] = option;
            return {
                id: this.context.generateRandomId(),
                key: this.context.ensureValidKey(key),
                label: value.formattedText
            };
        }) || [];

        const questionId = this.context.generateRandomId();
        const uniqueKey = this.context.ensureUniqueKey(path);

        const payload = {
            blueprintId: this.context.blueprintId,
            questionIdOpt: null,
            question: {
                id: questionId,
                key: this.context.ensureValidKey(uniqueKey),
                label: obj[WIDGET_PROPERTIES.UI_FORMATTED_TEXT] ?? "",
                inputType,
                afterFieldOpt: null,
                options
            },
            sessionId: this.generateSessionId()
        };

        this.context.addTransferredField(path, uniqueKey);
        return payload;
    }

    async process(mainSchema) {
        try {
            const widgets = this.findWidgets(mainSchema);
            for (const widget of widgets) {
                await createOrEditGatingQuestion(this.transformToPayload(widget));
            }
            return widgets.length;
        } catch (error) {
            console.error('Error processing Gating Question widgets:', error);
            throw error;
        }
    }
}

/**
 * Processor for Well widgets
 */
class WellProcessor extends WidgetProcessor {
    constructor(context) {
        super(context);
        this.widgetType = 'Paragraph';
    }

    findWidgets(mainSchema) {
        const { uiSchema, schema } = mainSchema;
        // If aliases array is empty, get all widgets of this type
        if (!this.context.aliases || this.context.aliases.length === 0) {
            return this.context.findWidgetsByType(uiSchema, this.widgetType);
        }

        // Otherwise, filter for widgets that match both the type and one of the aliases
        const results = [];
        this.context.aliases.forEach(alias => {
            const widget = this.context.findWidgetByKey(uiSchema, alias);
            if(widget) {
                const childAliases = findPropertyNamesUnderObject(schema, alias, 'null');
                childAliases.forEach(childAlias => {
                    const childWidget = this.context.findWidgetByKey(uiSchema, childAlias);
                    if (childWidget && childWidget.object && childWidget.object[WIDGET_PROPERTIES.UI_WIDGET] === this.widgetType) {
                        console.debug(`Found ${this.widgetType} with alias: ${childAlias}`);
                        results.push(childWidget);
                    }
                });
                if (widget.object && widget.object[WIDGET_PROPERTIES.UI_WIDGET] === this.widgetType) {
                    console.debug(`Found ${this.widgetType} with alias: ${alias}`);
                    results.push(widget);
                }
            }
        });

        return results;
    }

    /**
     * {
     *     "blueprintId": "bprrenqdw5m55mg851jr",
     *     "paragraphIdOpt": null,
     *     "paragraphMessage": {
     *       "id": "7BrAl0",
     *       "key": "defaultwell1",
     *       "label": "<p>defaultwell1</p>",
     *       "style": {
     *         "Default": {}
     *       },
     *       "afterFieldOpt": null
     *     },
     *     "sessionId": "blp-9ccf94e7-ead8-4aed-aca0-d1c4cad5c312"
     *   }
     * @param {*} wellData 
     * @returns 
     */
    transformToPayload(wellData) {
        const { path, object: obj } = wellData;
        const uniqueKey = this.context.ensureUniqueKey(path);
        let style = {};
        if (obj[WIDGET_PROPERTIES.UI_WELL_TYPE] === 'Info') {
            style = {
                Blue: {}
            };
        }
        else if (obj[WIDGET_PROPERTIES.UI_WELL_TYPE] === 'Warning') {
            style = {
                Orange: {}
            };
        }
        else {
            style = {
                Default: {}
            };
        }
        
        const payload = {
            blueprintId: this.context.blueprintId,
            paragraphIdOpt: null,
            paragraphMessage: {
                id: this.context.generateRandomId(),
                key: this.context.ensureValidKey(uniqueKey),
                label: obj[WIDGET_PROPERTIES.UI_FORMATTED_TEXT] ?? "",
                style,
                afterFieldOpt: null
            },
            sessionId: this.generateSessionId()
        };

        this.context.addTransferredField(path, uniqueKey);
        return payload;
    }
    

    async process(mainSchema) {
        try {
            const widgets = this.findWidgets(mainSchema);
            for (const widget of widgets) {
                await createOrEditWell(this.transformToPayload(widget));
            }
            return widgets.length;
        } catch (error) {
            console.error(`Error processing ${this.widgetType} widgets:`, error);
            throw error;
        }
    }
}
/**
 * Null processor for unsupported widget types
 */
class NullProcessor extends WidgetProcessor {
    async process() {
        console.log('No processor available for this widget type');
        return 0;
    }
}

/**
 * FormToBlueprint class for transferring form widgets to blueprint
 */
export default class FormToBlueprint {
    /**
     * @param {Object} config - Configuration object
     * @param {string} config.blueprintId - Blueprint ID
     * @param {string} config.latestVersionId - Latest version ID
     * @param {string} config.widgetType - Widget type to process
     * @param {Array} config.aliases - Aliases for widgets to filter by
     *                               - If empty, processes all widgets of specified type
     *                               - If provided, only processes widgets with matching aliases and type
     */
    constructor(config = {}) {
        this.blueprintId = config.blueprintId || '';
        this.latestVersionId = config.latestVersionId || '';
        this.widgetType = config.widgetType || '';
        this.aliases = config.aliases || [];
        this.existingIds = [];
        this.existingKeys = [];
        this.transferredFields = [];
        this.blueprintData = {};
    }

    /**
     * Main method to transfer form data to blueprint
     * @param {Object} jsonData - Form data
     * @param {Object} blueprintJson - Blueprint data
     * @returns {Object} - Report of transferred fields
     */
    async transfer(jsonData, blueprintJson) {
        console.log("Transferring widgets to blueprint...");

        const mainSchema = jsonData.formData.form.namespaceFormSchemaMap.main;
        this.sourceFormName = jsonData.formModel.name;
        this.blueprintData = blueprintJson;
        const {metadata} = this.blueprintData.blueprintInfo;

        const extracted = this.extractIdsAndKeys(metadata);
        this.existingIds = extracted.ids;
        this.existingKeys = extracted.keys;

        // Create appropriate processor for widget type
        const processor = WidgetProcessorFactory.createProcessor(this.widgetType, this);

        // Process widgets
        await processor.process(mainSchema);

        //Only save blueprint if there are widgets transferred
        if (this.transferredFields.length > 0) {
            await this.saveBlueprint();
        }

        return this.generateReport();
    }

    /**
     * Add a transferred field to tracking
     * @param {string} alias - Original path
     * @param {string} toAlias - New key
     */
    addTransferredField(alias, toAlias) {
        // original key, new key, status if it's the same
        this.transferredFields.push({
            alias,
            toAlias,
            same: alias === toAlias
        });
    }

    /**
     * Generate report of transferred fields
     * @returns {Object} - Report object
     */
    generateReport() {
        return {
            transferredFields: this.transferredFields,
            summary: {
                totalFields: this.transferredFields.length,
            },
        };
    }

    /**
     * Save blueprint version
     * @returns {Promise<void>}
     */
    async saveBlueprint() {
        const sessionId = `blp-${this.generateRandomId(8)}-${this.generateRandomId(4)}-${this.generateRandomId(4)}-${this.generateRandomId(4)}-${this.generateRandomId(12)}`;

        const payload = {
            blueprintId: this.blueprintId,
            parentVersionIdOpt: this.latestVersionId,
            name: `Import from ${this.sourceFormName} via White-tree`,
            //Noted down the transferred key list to note
            note: `Transferred keys: ${this.transferredFields.map(field => field.alias).join(', ')}`,
            formLogic: this.blueprintData.formLogic,
            sessionId
        };

        await createBlueprintVersion(payload);
    }

    /**
     * Find widgets by type in schema
     * @param {Object} obj - Schema to search
     * @param {string} widgetType - Widget type to find
     * @param {string} path - Current path
     * @param {Array} results - Results accumulator
     * @returns {Array} - Found widgets
     */
    findWidgetsByType(obj, widgetType, path = '', results = []) {
        // Base case: not an object
        if (!obj || typeof obj !== 'object') {
            return results;
        }

        // Check if current object has the specified ui:widget type
        if (obj[WIDGET_PROPERTIES.UI_WIDGET] === widgetType) {
            results.push({
                path: path,
                object: obj
            });
        }

        // Recursively check all properties
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                const newPath = path ? `${path}.${key}` : key;
                this.findWidgetsByType(obj[key], widgetType, newPath, results);
            }
        }

        return results;
    }

    /**
     * Find widget by key in schema
     * @param {Object} obj - Schema to search
     * @param {string} keyToFind - Key to find
     * @param {string} path - Current path
     * @returns {Object|null} - Found widget or null if not found
     */
    findWidgetByKey(obj, keyToFind, path = '') {
        // Base case: not an object
        if (!obj || typeof obj !== 'object') {
            return null;
        }

        // Check if the key exists directly in the object
        if (Object.prototype.hasOwnProperty.call(obj, keyToFind)) {
            return {
                path: path ? `${path}.${keyToFind}` : keyToFind,
                object: obj[keyToFind]
            };
        }

        // Recursively search in nested objects
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                const newPath = path ? `${path}.${key}` : key;
                const result = this.findWidgetByKey(obj[key], keyToFind, newPath);
                if (result) {
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * Generate a random ID
     * @param {number} length - Length of ID
     * @returns {string} - Unique ID
     */
    generateRandomId(length = 6) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result;
        do {
            result = '';
            for (let i = 0; i < length; i++) {
                result += characters.charAt(Math.floor(Math.random() * characters.length));
            }
        } while (this.existingIds.includes(result));
        this.existingIds.push(result);
        return result;
    }

    /**
     * Extract IDs and keys from data
     * @param {Object} data - Data to extract from
     * @returns {Object} - Object with ids and keys arrays
     */
    extractIdsAndKeys(data) {
        const ids = [];
        const keys = [];

        const traverse = (obj) => {
            if (Array.isArray(obj)) {
                obj.forEach(item => traverse(item));
            } else if (typeof obj === 'object' && obj !== null) {
                if (obj.id) {
                    ids.push(obj.id);
                }
                if (obj.key) {
                    keys.push(obj.key);
                }
                Object.values(obj).forEach(value => traverse(value));
            }
        };

        traverse(data);
        return { ids, keys };
    }

    /**
     * Ensure a key is unique
     * @param {string} key - Key to check
     * @returns {string} - Unique key
     */
    ensureUniqueKey(key) {
        let uniqueKey = key;
        let postfix = 1;

        while (this.existingKeys.includes(uniqueKey)) {
            uniqueKey = `${key}_${postfix}`;
            postfix++;
        }

        this.existingKeys.push(uniqueKey);
        return uniqueKey;
    }
    /**
     * Ensure a key is having proper format for blueprint 
     *  - No special characters
     *  - No spaces - replace with underscore
     *  - No leading or trailing underscores
     *  - No consecutive underscores
     *  - No more than 20 characters
     *  - No uppercase
     */
    ensureValidKey(key) {
        // Replace spaces with underscores
        let formattedKey = key.replace(/ /g, '_');
        // Remove special characters
        formattedKey = formattedKey.replace(/[^a-zA-Z0-9_]/g, '');
        // Remove leading and trailing underscores
        formattedKey = formattedKey.replace(/^_+|_+$/g, '');
        // Limit to 20 characters
        formattedKey = formattedKey.substring(0, 30);
        // Convert to lowercase
        formattedKey = formattedKey.toLowerCase();
        return formattedKey;
    }
    /**
     * Renders the transfer report in a Bootstrap 5 format
     * @param {Object} results - Transfer results containing transferredFields and summary
     * @returns {string} - HTML string to render
     */
    renderReport(results) {
        // If no results or empty transferredFields, show a message
        if (!results || !results.transferredFields || results.transferredFields.length === 0) {
            return `
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    No fields were transferred. Please check your configuration and try again.
                </div>
            </div>
        `;
        }

        // Get total fields from summary
        const totalFields = results.summary.totalFields;

        // Count fields with name changes
        const nameChangesCount = results.transferredFields.filter(field => !field.same).length;

        // Create the HTML for the report
        const html = `
        <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Transfer Report</h4>
                <div>
                    <span class="badge bg-light text-success fs-6">${totalFields} Field${totalFields !== 1 ? 's' : ''} Transferred</span>
                    ${nameChangesCount > 0 ? `<span class="badge bg-light text-warning fs-6 ms-2">${nameChangesCount} Name Change${nameChangesCount !== 1 ? 's' : ''}</span>` : ''}
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-success mb-4" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                Successfully transferred ${totalFields} field${totalFields !== 1 ? 's' : ''} to the blueprint.
            </div>
            
            <!-- Summary of name changes -->
            ${(() => {
            const nameChanges = results.transferredFields.filter(field => !field.same).length;
            return nameChanges > 0 ? `
                <div class="alert alert-warning mb-4" role="alert">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    <strong>${nameChanges}</strong> field${nameChanges !== 1 ? 's' : ''} had ${nameChanges !== 1 ? 'their' : 'its'} name changed during transfer. 
                    These fields are highlighted in the table below.
                </div>
                ` : '';
        })()}
            
            <h5 class="card-title">Transferred Fields</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">Original Alias</th>
                            <th scope="col">Blueprint Alias</th>
                            <th scope="col">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${results.transferredFields.map((field, index) => `
                            <tr ${!field.same ? 'class="table-warning"' : ''}>
                                <th scope="row">${index + 1}</th>
                                <td>
                                    <code class="bg-light p-1 rounded">${field.alias}</code>
                                </td>
                                <td>
                                    <span class="badge ${field.same ? 'bg-primary' : 'bg-warning text-dark'}">${field.toAlias}</span>
                                </td>
                                <td>
                                    ${field.same
            ? '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i> Unchanged</span>'
            : '<span class="badge bg-warning text-dark"><i class="bi bi-arrow-right-circle-fill me-1"></i> Name Changed</span>'}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        </div>
    `;

        // Show the results content
        const resultsContent = document.getElementById('formToBlueprintResultsContent');
        if (resultsContent) {
            resultsContent.innerHTML = html;
            resultsContent.style.display = 'block';
        }

        return html;
    }
}