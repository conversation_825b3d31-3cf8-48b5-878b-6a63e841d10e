import { DOM_LOCATORS, CSS_CLASSES } from '../constants/domLocators';

export default class FontMismatchDetector {
    constructor() {}

    async findMismatches(annotationFile, compareCondition, expectedFontName, expectedFontSize) {
        let mismatches = [];

        try {
            const {annotationData} = annotationFile;

            // NEW Export
            if ("pdfObjectDTO" in annotationData) {
                annotationData.pdfObjectDTO.forEach(pdfField => {
                    if ("fontNameOpt" in pdfField && "fontSizeOpt" in pdfField) {
                        this.collectMismatches(mismatches, pdfField, compareCondition, expectedFontName, expectedFontSize);
                    }
                });
            }

            // OLD Export
            else if ("pdfObjects" in annotationData) {
                annotationData.pdfObjects.forEach(pdfField => {
                    if ("PdfTextField" in pdfField) {
                        this.collectMismatches(mismatches, pdfField.PdfTextField, compareCondition, expectedFontName, expectedFontSize);
                    }
                });
            }

        } catch (error) {
            console.error("⛔️ Failed to read JSON file: ", error);
            return {error: "⛔️ Failed to read JSON file. Please try again."};
        }

        return mismatches;
    }

    async collectMismatches(mismatches, checkingField, compareCondition, expectedFontName, expectedFontSize) {
        const {name, fontNameOpt, fontSizeOpt} = checkingField;
        const isInvalidFontName = fontNameOpt === null || fontNameOpt === "";
        const isInvalidFontSize = fontSizeOpt === null;
        const expectedFontSizeNum = Number(expectedFontSize);
        let condition;

        if (compareCondition === "name and size") {
            condition = (fontNameOpt !== expectedFontName) || (fontSizeOpt !== expectedFontSizeNum);

        } else if (compareCondition === "name only") {
            condition = (fontNameOpt !== expectedFontName);

        } else if (compareCondition === "size only") {
            condition = (fontSizeOpt !== expectedFontSizeNum);
        }

        if (condition) {
            if (isInvalidFontName && isInvalidFontSize) {
                mismatches.push({
                    alias: name,
                    fontName: "N/A",
                    fontSize: "N/A",
                });

            } else if (isInvalidFontName) {
                mismatches.push({
                    alias: name,
                    fontName: "N/A",
                    fontSize: fontSizeOpt,
                });

            } else if (isInvalidFontSize) {
                mismatches.push({
                    alias: name,
                    fontName: fontNameOpt,
                    fontSize: "N/A",
                });

            } else {
                mismatches.push({
                    alias: name,
                    fontName: fontNameOpt,
                    fontSize: fontSizeOpt,
                });
            }
        }
    }

    renderReport(mismatches) {
        const resultMessage = document.getElementById(DOM_LOCATORS.RESULT_MESSAGE);
        const documentNameHeader = document.getElementById(DOM_LOCATORS.DOCUMENT_NAME_HEADER);
        const resultTable = document.querySelector(DOM_LOCATORS.FONT_MISMATCH_DETECTOR_RESULT_TABLE);
        resultTable.innerHTML = "";
        resultMessage.innerHTML = `⚠️ 💀 Found <strong>${mismatches.length}</strong> mismatched aliases! 💀 ⚠️`;

        if (mismatches.length === 0) {
            const row = resultTable.insertRow();
            const cell = row.insertCell();
            cell.colSpan = 4;
            cell.setAttribute("style", "color: black");
            cell.textContent = "✅ No mismatches found ✅";

        } else {
            mismatches.forEach(({alias, fontName, fontSize}, index) => {
                const row = resultTable.insertRow();
                row.insertCell().textContent = index + 1;

                const aliasCell = row.insertCell();
                aliasCell.innerHTML = `<code class="${CSS_CLASSES.COPY_TO_CLIPBOARD}">${alias}</code>`;
                aliasCell.className = `text-left ${CSS_CLASSES.COPY_TO_CLIPBOARD} text-wrap`;
                aliasCell.setAttribute("data-alias", alias);

                row.insertCell().textContent = fontName;
                row.insertCell().textContent = fontSize;
            });
        }
    }
}
