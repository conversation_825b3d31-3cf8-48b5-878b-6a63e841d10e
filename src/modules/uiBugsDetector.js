import lodash from "lodash";
import {WIDGET_TYPES, WIDGET_PROPERTIES, OPTION_PROPERTIES} from "../constants/formWidgetConstants";
import { CSS_CLASSES } from '../constants/domLocators';

export default class UiBugsDetector {

    constructor(config = {}) {
        this.EXPECTED_PAGE_SUBTITLE = config.expectedPageSubtitle || '';
        this.EMBEDDED_PDF_PREFIX = config.embeddedPdfPrefix || '';

        this.HELP_TEXT_W8 = '<p>All relevant Form W-8s are available at: <a href="https://apps.irs.gov/app/picklist/list/priorFormPublication.html?sortColumn=currentYearRevDate&indexOfFirstRow=0&value=W-8&criteria=formNumber&resultsPerPage=200&isDescending=true" rel="noopener noreferrer" target="_blank">https://apps.irs.gov/app/picklist/list/priorFormPublication.html?sortColumn=currentYearRevDate&indexOfFirstRow=0&value=W-8&criteria=formNumber&resultsPerPage=200&isDescending=true</a></p>';
        this.HELP_TEXT_W8_LONG = '<p>Below are links to the IRS website for the most common W-8 forms:</p><ul><li>Form W-8BEN - <a href="https://www.irs.gov/pub/irs-pdf/fw8ben.pdf" rel="noopener noreferrer" target="_blank">https://www.irs.gov/pub/irs-pdf/fw8ben.pdf</a></li><li>Form W-8BEN-E - <a href="https://www.irs.gov/pub/irs-pdf/fw8bene.pdf" rel="noopener noreferrer" target="_blank">https://www.irs.gov/pub/irs-pdf/fw8bene.pdf</a></li><li>Form W-8EXP - <a href="https://www.irs.gov/pub/irs-pdf/fw8exp.pdf" rel="noopener noreferrer" target="_blank">https://www.irs.gov/pub/irs-pdf/fw8exp.pdf</a></li><li>Form W-8IMY - <a href="https://www.irs.gov/pub/irs-pdf/fw8imy.pdf" rel="noopener noreferrer" target="_blank">https://www.irs.gov/pub/irs-pdf/fw8imy.pdf</a></li><li>Form W-8ECI - <a href="https://www.irs.gov/pub/irs-pdf/fw8eci.pdf" rel="noopener noreferrer" target="_blank">https://www.irs.gov/pub/irs-pdf/fw8eci.pdf</a></li></ul><p>Please check with your tax advisor(s) on which form is appropriate for your situation.</p>';
        this.STRIP_HTML_HELP_TEXT_W8 = 'All relevant Form W-8s are available at: https://apps.irs.gov/app/picklist/list/priorFormPublication.html?sortColumn=currentYearRevDate&indexOfFirstRow=0&value=W-8&criteria=formNumber&resultsPerPage=200&isDescending=true';
        this.STRIP_HTML_HELP_TEXT_W8_LONG = 'Below are links to the IRS website for the most common W-8 forms:Form W-8BEN - https://www.irs.gov/pub/irs-pdf/fw8ben.pdfForm W-8BEN-E - https://www.irs.gov/pub/irs-pdf/fw8bene.pdfForm W-8EXP - https://www.irs.gov/pub/irs-pdf/fw8exp.pdfForm W-8IMY - https://www.irs.gov/pub/irs-pdf/fw8imy.pdfForm W-8ECI - https://www.irs.gov/pub/irs-pdf/fw8eci.pdfPlease check with your tax advisor(s) on which form is appropriate for your situation.';
        this.HELP_TEXT_W9 = '<p>Available at <a href="http://www.irs.gov/pub/irs-pdf/fw9.pdf" rel="noopener noreferrer" target="_blank">www.irs.gov/pub/irs-pdf/fw9.pdf</a></p>';
        this.HELP_TEXT_W9_LONG = '<p>You can download W-9 Form here: <a href="http://www.irs.gov/pub/irs-pdf/fw9.pdf" rel="noopener noreferrer" target="_blank">www.irs.gov/pub/irs-pdf/fw9.pdf</a></p>';
        this.STRIP_HTML_HELP_TEXT_W9 = 'Available at https://www.irs.gov/pub/irs-pdf/fw9.pdf';
        this.STRIP_HTML_HELP_TEXT_W9_LONG = 'You can download W-9 Form here: https://www.irs.gov/pub/irs-pdf/fw9.pdf';
        this.TITLE_CASE_REGEX = /^\s*(?:\s*[A-Z][a-z]*\s*\b)+\s*$/;
        this.HTML_TAG_REGEX = /<\/?[^>]+(>|$)/g;
        this.WHITESPACE_ENCODE_REGEX = /&nbsp;/g;
        this.RIGHT_ASTERISK_COLOR_REQUIRED_FIELDS_REGEX = /style=.*color: rgb\(219, 55, 55\);.*>\s*\*/;
        this.RIGHT_ASTERISK_COLOR_RECOMMENDED_FIELDS_REGEX = /style=.*color: rgb\(217, 130, 43\);.*>\s*\*/;
        this.REDUNDANT_COLON_AT_LABEL_REGEX = /:\s*\*?\s*$/g;
        this.listFieldsBelow = [];
        this.fieldBelowRelationships = [];
    }

    async analyze(jsonData) {
        const {uiSchema} = jsonData.formData.form.namespaceFormSchemaMap.main;
        const schemaTOC = lodash.map(jsonData.formData.form.namespaceFormSchemaMap.main.schema.properties, 'name');
        const {embeddedPdf} = jsonData.formData;
        const {name, latestVersionId, versionCount} = jsonData.formModel;
        const versionOpt = jsonData.versionOpt || {};
        const {formVersionId = null, versionNumber = null} = versionOpt;
        const {rules} = jsonData.formData.form;

        const metadata = {
            name,
            latestVersionId,
            versionCount,
            formVersionId,
            versionNumber
        };

        let issues = {
            invalidOrMissedSubtitles: [],
            invalidSupportingDocNames: [],
            invalidHelpTextsW8W9: [],
            invalidEmbeddedPDFs: [],
            groupsAndPagesMissedDisabledTooltips: [],
            wrongIndentationFieldsBelow: [],
            invalidHeading2TitleFormat: [],
            invalidHeading2SubTitleFormat: [],
            textboxesWithEmptyMapping: [],
            duplicatedRadioMappings: [],
            blankParagraphs: [],
            wrongColorAsteriskRequiredFields: [],
            wrongColorAsteriskRecommendedFields: [],
            redundantColonAtLabels: [],
            notSetMaxLengthRepeatable: [],
            missingHideRuleForFieldBelow: [],
        };

        // Analyze each schema widget object
        for (let key in uiSchema) {
            const widget = uiSchema[key];
            this.analyzeSchemaObject(key, widget, schemaTOC, issues);
        }

        // Process fields below indentation
        this.checkFieldsBelowIndentation(uiSchema, this.listFieldsBelow, issues.wrongIndentationFieldsBelow);

        // Check embedded PDFs
        if (this.EMBEDDED_PDF_PREFIX) {
            this.checkEmbeddedPDFs(embeddedPdf, issues.invalidEmbeddedPDFs);
        }

        // Check duplicated radio mappings
        this.checkDuplicatedRadioMappings(uiSchema, issues.duplicatedRadioMappings);

        // Check missing enabled rule for fields below
        this.checkMissingHideRuleForFieldsBelow(this.fieldBelowRelationships, rules, issues.missingHideRuleForFieldBelow);

        return this.generateReport(metadata, issues);
    }

    analyzeSchemaObject(key, widget, schemaTOC, issues) {
        const widgetType = lodash.get(widget, WIDGET_PROPERTIES.UI_WIDGET);
        const formattedText = lodash.get(widget, WIDGET_PROPERTIES.UI_FORMATTED_TEXT);
        const isInvisible = lodash.get(widget, WIDGET_PROPERTIES.UI_INVISIBLE);
        const mappingFields = lodash.get(widget, WIDGET_PROPERTIES.UI_PDF_MAPPING);
        const headerType = lodash.get(widget, WIDGET_PROPERTIES.UI_HEADER_TYPE);
        const subtitle = lodash.get(widget, WIDGET_PROPERTIES.UI_SUBTITLE);
        const supportingFileGroup = lodash.get(widget, WIDGET_PROPERTIES.UI_SUPPORTING_FILE_GROUP);
        const multipleOption = lodash.get(widget, WIDGET_PROPERTIES.UI_MULTIPLE_OPTION);
        const maxItemsRepeatable = lodash.get(widget, WIDGET_PROPERTIES.UI_MAX_LENGTH);
        const isRequiredField = lodash.get(widget, WIDGET_PROPERTIES.UI_REQUIRED);
        const isRecommendedField = lodash.get(widget, WIDGET_PROPERTIES.UI_RECOMMENDED);
        const hasTooltipForDisabledState = lodash.get(widget, WIDGET_PROPERTIES.UI_TOOLTIP_FOR_DISABLED_STATE);
        const title = lodash.get(widget, WIDGET_PROPERTIES.UI_TITLE);
        const wellType = lodash.get(widget, WIDGET_PROPERTIES.UI_WELL_TYPE);

        // Check headers
        if (widgetType === WIDGET_TYPES.HEADER) {
            this.checkHeader(key, title, headerType, subtitle, issues);
        }

        // Check file groups
        if (widgetType === WIDGET_TYPES.FILE_GROUP && supportingFileGroup) {
            this.checkFileGroup(key, supportingFileGroup, issues);
        }

        // Check disabled tooltips
        if (schemaTOC.includes(key) && !isInvisible && !hasTooltipForDisabledState) {
            issues.groupsAndPagesMissedDisabledTooltips.push({
                alias: key,
                note: `Widget type: ${widgetType}`
            });
        }

        // Check TextBox mapping
        if (widgetType === WIDGET_TYPES.TEXTBOX && (!mappingFields || mappingFields?.length === 0)) {
            issues.textboxesWithEmptyMapping.push({
                alias: key,
            });
        }

        // Check Paragraph
        if (widgetType === WIDGET_TYPES.PARAGRAPH && (!formattedText || formattedText === "") && !isInvisible) {
            issues.blankParagraphs.push({
                alias: key,
            });
        }

        // Check Repeatable
        if (widgetType === WIDGET_TYPES.REPEATABLE && maxItemsRepeatable && maxItemsRepeatable <= 1) {
            issues.notSetMaxLengthRepeatable.push({
                alias: key,
            });
        }

        // Check asterisk color of required and recommended fields
        if (formattedText?.includes("*")) {
            if (isRequiredField && !this.decodeWhiteSpaces(formattedText).match(this.RIGHT_ASTERISK_COLOR_REQUIRED_FIELDS_REGEX)) {
                issues.wrongColorAsteriskRequiredFields.push({
                    alias: key,
                });
            }

            if (isRecommendedField && !this.decodeWhiteSpaces(formattedText).match(this.RIGHT_ASTERISK_COLOR_RECOMMENDED_FIELDS_REGEX)) {
                issues.wrongColorAsteriskRecommendedFields.push({
                    alias: key,
                });
            }
        }

        // Check redundant colon at label
        if ((widgetType === WIDGET_TYPES.TEXTBOX || widgetType === WIDGET_TYPES.TEXTAREA) && formattedText) {
            let actualStrippedLabel = this.stripHTMLTags(this.decodeWhiteSpaces(formattedText));

            if (!isInvisible && actualStrippedLabel.match(this.REDUNDANT_COLON_AT_LABEL_REGEX)) {
                issues.redundantColonAtLabels.push({
                    alias: key,
                });
            }
        }

        // Retrieve fieldsBelow list and store relationships for later checking
        if (widgetType === WIDGET_TYPES.RADIO || widgetType === WIDGET_TYPES.MULTIPLE_CHECKBOX) {
            if (multipleOption) {
                for (let option of multipleOption["options"]) {
                    if (option[1][OPTION_PROPERTIES.FIELDS_BELOW]) {
                        const fieldsBelow = option[1][OPTION_PROPERTIES.FIELDS_BELOW];
                        this.listFieldsBelow.push(...fieldsBelow);
                        
                        // Store parent-fieldBelow relationships for gr.enable rule checking
                        for (let fieldBelowAlias of fieldsBelow) {
                            this.fieldBelowRelationships.push({
                                parentWidgetKey: key,
                                fieldBelowAlias: fieldBelowAlias
                            });
                        }
                    }
                }
            }
        }
    }

    checkFieldsBelowIndentation(uiSchema, listFieldsBelow, wrongIndentationFieldsBelow) {
        let filteredUiSchema = Object.keys(uiSchema)
            .filter((key) => listFieldsBelow.includes(key))
            .reduce((result, key) => {
                result[key] = uiSchema[key];
                return result;
            }, {});

        for (let [key, fieldBelowValue] of Object.entries(filteredUiSchema)) {
            let actualIndentation = fieldBelowValue[WIDGET_PROPERTIES.UI_INDENTATION];

            if (actualIndentation && actualIndentation !== "24px") {
                wrongIndentationFieldsBelow.push({
                    alias: key,
                    actual: actualIndentation,
                    expected: "24px",
                });
            }
        }
    }

    checkEmbeddedPDFs(embeddedPdfs, invalidEmbeddedPDFs) {
        for (let [key, value] of Object.entries(embeddedPdfs)) {
            if (!value[0].startsWith(this.EMBEDDED_PDF_PREFIX)) {
                invalidEmbeddedPDFs.push({
                    alias: value[0],
                });
            }
        }
    }

    checkDuplicatedRadioMappings(uiSchema, duplicatedRadioMappings) {
        let mappings = new Map();

        for (let [key, value] of Object.entries(uiSchema)) {
            if (value[WIDGET_PROPERTIES.UI_WIDGET] === WIDGET_TYPES.RADIO) {
                let mapping = value[WIDGET_PROPERTIES.UI_PDF_MAPPING];
                if (mapping) {
                    if (mappings.has(mapping)) {
                        duplicatedRadioMappings.push({
                            alias: key,
                            actual: mapping,
                        });
                    } else {
                        mappings.set(mapping, key);
                    }
                }
            }
        }
    }

    checkMissingHideRuleForFieldsBelow(fieldBelowRelationships, formRules, missingHideRuleForFieldBelow) {
        for (const relationship of fieldBelowRelationships) {
            const { parentWidgetKey, fieldBelowAlias } = relationship;
            let ruleExistsForFieldBelow = false;

            for (const rule of formRules) {
                const ruleLogic = rule.value;

                // Check if rule function has the parent widget as parameter
                const parentWidgetPattern = new RegExp(`function\\s*\\(([^)]*(\\b${parentWidgetKey}\\b)[^)]*)\\)`);
                const hasParentWidget = parentWidgetPattern.test(ruleLogic);

                // Check if rule contains gr.enable for this field below
                const grEnablePattern = new RegExp(`gr\\.enable\\(\\s*(afs\\.logic\\.main|logic)\\.${fieldBelowAlias}\\b`);
                const hasGrEnable = grEnablePattern.test(ruleLogic);

                // Check if rule contains fieldBelowAlias.show, example: "gr.show(logic.bankaba_wireinstructions2"
                const hasFieldBelowShowOrHideLogicNewPattern = new RegExp(`gr\\.(show|hide)\\(\\s*(afs\\.logic\\.main|logic)\\.${fieldBelowAlias}\\b`);
                const hasFieldBelowShowOrHideLogicNew = hasFieldBelowShowOrHideLogicNewPattern.test(ruleLogic);

                // Check if rule contains fieldBelowAlias.hide, example: "afs.logic.main.textbox1.show"
                const hasFieldBelowShowOrHideLogicPattern = new RegExp(`(logic\\.main|logic)\\.${fieldBelowAlias}\\.(show|hide)`);
                const hasFieldBelowShowOrHideLogic = hasFieldBelowShowOrHideLogicPattern.test(ruleLogic);

                // Check if rule contains show logic with mixcalc_v3, example: ".unit\n    .show(cta)\n    .apply(logic.indicate_another_exemption_not_registered_cta, ctx = indicate_another_exemption_not_registered_cta)"
                const hasShowLogicMixCalcV3Pattern = new RegExp(`\\.unit\\n\\s*\\.show*\\(.*\\)\\n\\s*\\.apply\\(logic\\.${fieldBelowAlias}\\, ctx = ${fieldBelowAlias}\\)`);
                const hasShowLogicMixCalcV3 = hasShowLogicMixCalcV3Pattern.test(ruleLogic);

                if (hasParentWidget && (hasGrEnable || hasFieldBelowShowOrHideLogicNew || hasFieldBelowShowOrHideLogic || hasShowLogicMixCalcV3)) {
                    ruleExistsForFieldBelow = true;
                    break;
                }
            }

            if (!ruleExistsForFieldBelow) {
                missingHideRuleForFieldBelow.push({
                    alias: fieldBelowAlias,
                    note: `Parent widget: <code>${parentWidgetKey}</code> - No corresponding show/hide rule found for fieldBelow: <code>${fieldBelowAlias}</code>`
                });
            }
        }
    }

    checkHeader(key, title, headerType, subtitle, issues) {
        if (headerType === "heading2") {
            if (title && !this.TITLE_CASE_REGEX.test(title)) {
                issues.invalidHeading2TitleFormat.push({
                    alias: key,
                    actual: title,
                });
            }
            if (subtitle && subtitle !== subtitle.toUpperCase()) {
                issues.invalidHeading2SubTitleFormat.push({
                    alias: key,
                    actual: subtitle,
                });
            }
        } else if (headerType === "heading1" && subtitle !== this.EXPECTED_PAGE_SUBTITLE) {
            issues.invalidOrMissedSubtitles.push({
                alias: key,
                actual: subtitle,
            });
        }
    }

    checkFileGroup(key, supportingFileGroup, issues) {
        let files = supportingFileGroup["files"];

        for (let fileKey in files) {
            let description = files[fileKey]["description"];
            let actualHelpText = files[fileKey]["helpText"];
            let stripHTMLActualHelpText = this.stripHTMLTags(actualHelpText);

            if (this.isInvalidFileName(description)) {
                issues.invalidSupportingDocNames.push({
                    alias: key,
                    note: `<code>${fileKey}</code>: ${description}`,
                });
            }

            if (this.isIncorrectHelpTextW8(description, actualHelpText, stripHTMLActualHelpText)) {
                issues.invalidHelpTextsW8W9.push({
                    alias: key,
                    actual: actualHelpText,
                    expected: `\n<strong>WHETHER:</strong>\n${this.HELP_TEXT_W8}\n<strong>OR:</strong>\n${this.HELP_TEXT_W8_LONG}`,
                    note: `File alias: ${fileKey}, Tax Form name: ${description}`
                });
            }

            if (this.isIncorrectHelpTextW9(description, actualHelpText, stripHTMLActualHelpText)) {
                issues.invalidHelpTextsW8W9.push({
                    alias: key,
                    actual: actualHelpText,
                    expected: `\n<strong>WHETHER:</strong>\n${this.HELP_TEXT_W9}\n<strong>OR:</strong>\n${this.HELP_TEXT_W9_LONG}`,
                    note: `File alias: ${fileKey}, Tax Form name: ${description}`
                });
            }
        }
    }

    isInvalidFileName(description) {
        return description.endsWith(':') || description.endsWith(';') || description.endsWith('.');
    }

    isIncorrectHelpTextW8(description, actualHelpText, stripHTMLActualHelpText) {
        return description.toLowerCase().includes('w-8') &&
            (actualHelpText !== this.HELP_TEXT_W8 && stripHTMLActualHelpText !== this.STRIP_HTML_HELP_TEXT_W8) &&
            (actualHelpText !== this.HELP_TEXT_W8_LONG && stripHTMLActualHelpText !== this.STRIP_HTML_HELP_TEXT_W8_LONG);
    }

    isIncorrectHelpTextW9(description, actualHelpText, stripHTMLActualHelpText) {
        return description.toLowerCase().includes('w-9') &&
            (actualHelpText !== this.HELP_TEXT_W9 && stripHTMLActualHelpText !== this.STRIP_HTML_HELP_TEXT_W9) &&
            (actualHelpText !== this.HELP_TEXT_W9_LONG && stripHTMLActualHelpText !== this.STRIP_HTML_HELP_TEXT_W9_LONG);
    }

    formatCategoryName(category) {
        return category
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase());
    }

    // Helper methods
    decodeWhiteSpaces(text) {
        return text.replaceAll(this.WHITESPACE_ENCODE_REGEX, " ");
    }

    stripHTMLTags(text) {
        return text.replaceAll(this.HTML_TAG_REGEX, "");
    }

    // Reporting
    generateReport(metadata, issues) {
        return {
            metadata: metadata,
            summary: {
                totalIssues: Object.values(issues).reduce((sum, arr) => sum + arr.length, 0),
                issuesByType: Object.fromEntries(
                    Object.entries(issues).map(([key, value]) => [key, value.length])
                )
            },
            details: issues
        };
    }

    renderReport(results) {
        return `
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Analyze Report</h4>
            </div>
            <div class="card-body">
                <div style="color: red">
                    ⚠️ <strong>Warning</strong>: 
                    Please note that this tool cannot replace your judgment in identifying bugs. Its purpose is to suggest potential issues for your consideration. ⚠️
                </div>
                <div>
                    We are committed to keeping the document up-to-date. 
                    <a href="https://www.notion.so/anduin/Utilizing-JS-code-and-Postman-to-promptly-detect-UI-bugs-potential-bugs-ec548f94a2ee4adf97ae38e339b65291#6f920da3e5b84327a3eece930d4d01cf">
                    Visit our Notion page</a> for more information.
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5 class="alert-heading">Form Name: <strong>${results.metadata.name}</strong></h5>
                    ${results.metadata.latestVersionId && results.metadata.formVersionId && (results.metadata.latestVersionId === results.metadata.formVersionId) ?
                        '' :
                        `<ul>
                            <li class="my-1">Latest Version ID: <code class="${CSS_CLASSES.COPY_TO_CLIPBOARD}">${results.metadata.latestVersionId}</code></li>
                            <li class="my-1">Current/Latest version: <strong>${results.metadata.versionNumber}/${results.metadata.versionCount}</strong></li>
                        </ul>`
                    }
                    <p class="mb-0">Total Issues Found: <strong>${results.summary.totalIssues}</strong></p>
                </div>
                
                ${Object.entries(results.details)
                .filter(([_, issues]) => issues.length > 0)
                .map(([category, issues]) => `
                    <div class="card mb-3">
                        <div class="card-header ${CSS_CLASSES.UI_BUGS_GROUP_HEADER} d-flex justify-content-between align-items-center" 
                            data-bs-toggle="collapse" data-bs-target="#collapse${category}" aria-expanded="false" aria-controls="collapse${category}" 
                            href="#collapse${category}" role="button" aria-expanded="false" aria-controls="collapse${category}">
                            <h6 class="mb-0">${this.formatCategoryName(category)}</h6>
                            <div>
                                <span class="badge ${CSS_CLASSES.BG_DANGER} ${CSS_CLASSES.ME_2}">${issues.length} issues</span>
                                <i class="bi ${CSS_CLASSES.CHEVRON_DOWN}"></i>
                            </div>
                        </div>
                        <div class="collapse" id="collapse${category}">
                            <div class="card-body">
                                <div class="list-group">
                                    ${issues.map(issue => `
                                        <div class="list-group-item">
                                            <p class="mb-1"><strong>Alias:</strong> <code class="${CSS_CLASSES.COPY_TO_CLIPBOARD}">${issue.alias}</code></p>
                                            ${issue.actual ? `<p class="mb-1"><strong>Actual:</strong> <span>${issue.actual}</span></p>` : ''}
                                            ${issue.expected ? `<p class="mb-1"><strong>Expected:</strong> <span>${issue.expected}</span></p>` : ''}
                                            ${issue.note ? `<p class="mb-1"><small class="${CSS_CLASSES.TEXT_MUTED}"><strong>Note:</strong> ${issue.note}</small></p>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>`)
                .join('')}
            </div>
        `;
    }
}
