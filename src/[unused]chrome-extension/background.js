console.log('Background script loaded');

const DOMAINS_TO_MONITOR = [
    "*://*.anduin.app/*",
    "*://portal.eu.anduin.app/*",
];

if (chrome.runtime.onInstalled) {
    chrome.runtime.onInstalled.addListener(() => {
        console.log('onInstalled event fired');
        if (chrome.declarativeNetRequest && chrome.declarativeNetRequest.updateDynamicRules) {
            console.log('Updating dynamic rules');
            chrome.declarativeNetRequest.updateDynamicRules({
                removeRuleIds: [1],
                addRules: [{
                    id: 1,
                    priority: 1,
                    action: {
                        type: "modifyHeaders",
                        responseHeaders: [{
                            header: "X-Captured-By-Extension",
                            operation: "set",
                            value: "true"
                        }]
                    },
                    condition: {
                        urlFilter: "https://portal.eu.anduin.app/api/v3/form/getForm",
                        resourceTypes: ["xmlhttprequest"]
                    }
                }]
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('Error updating rules:', chrome.runtime.lastError);
                } else {
                    console.log('Rules updated successfully');
                }
            });
        }
    });
}

if (chrome.declarativeNetRequest && chrome.declarativeNetRequest.onRuleMatchedDebug) {
    chrome.declarativeNetRequest.onRuleMatchedDebug.addListener(
        (info) => {
            console.log('Rule matched:', info);
            if (isMonitoredDomain(info.request.url)) {
                console.log('Matched request:', info.request);
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {action: "fetchData", url: info.request.url});
                    }
                });
            }
        }
    );
} else {
    console.error('declarativeNetRequest.onRuleMatchedDebug is not available');
}

function isMonitoredDomain(url) {
    return DOMAINS_TO_MONITOR.some(domain => {
        const regex = new RegExp(domain.replace(/\*/g, '.*'));
        return regex.test(url);
    });
}

if (chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('Message received in background:', message);
        if (message.action === "dataFetched") {
            chrome.storage.local.get('collectedData', function (result) {
                let collectedData = result.collectedData || [];
                collectedData.push({
                    timestamp: new Date().toISOString(),
                    url: message.url,
                    data: message.data
                });
                chrome.storage.local.set({collectedData: collectedData}, function () {
                    console.log('Data saved:', message.url);
                });
            });
        }
    });
}