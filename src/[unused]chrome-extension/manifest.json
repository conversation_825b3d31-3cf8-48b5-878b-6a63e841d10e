{"manifest_version": 3, "name": "Anduin Internal Chrome Extension", "version": "1.0", "description": "Collect specific JSON files from Anduin system's response and send to the visualization tools", "permissions": ["storage", "declarativeNetRequest", "declarativeNetRequestFeedback", "activeTab"], "host_permissions": ["*://*.anduin.app/*", "https://portal.eu.anduin.app/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://*.anduin.app/*", "*://portal.eu.anduin.app/*"], "js": ["contentScript.js"]}], "icons": {"16": "/icons/icon16.png", "48": "/icons/icon48.png", "128": "/icons/icon128.png"}, "action": {"default_icon": {"16": "/icons/icon16.png", "48": "/icons/icon48.png", "128": "/icons/icon128.png"}}, "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules.json"}]}}