(function() {
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
        // Gọi request gốc
        const response = await originalFetch.apply(this, args);
        // Clone response để có thể đọc nội dung mà không ảnh hưởng đến luồng gốc
        const responseClone = response.clone();
        // Đọc response text (hoặc response.json() nếu bạn biết dữ liệu trả về là JSON)
        responseClone.text().then(bodyText => {
            // Gửi thông tin qua window.postMessage để content script bắt lấy
            window.postMessage({
                type: "FETCH_INTERCEPT",
                url: args[0],
                method: args[1]?.method || "GET",
                responseBody: bodyText
            }, "*");
        });
        return response;
    };
})();
