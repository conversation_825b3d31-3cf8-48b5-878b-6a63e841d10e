console.log('Content script loaded');

// chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
//     console.log('Message received in content script:', message);
//     if (message.action === "fetchData") {
//         fetch(message.url)
//             .then(response => response.json())
//             .then(data => {
//                 chrome.runtime.sendMessage({
//                     action: "dataFetched",
//                     url: message.url,
//                     data: data
//                 });
//             })
//             .catch(error => console.error('Error fetching data:', error));
//     }
// });

(function() {
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('fetch-interceptor.modules');
    script.onload = function() {
        this.remove();
    };
    (document.head || document.documentElement).appendChild(script);
})();

// Lắng nghe message từ window
window.addEventListener("message", (event) => {
    // <PERSON><PERSON><PERSON> tra t<PERSON>h hợp lệ của event, ví dụ event.source === window, và type message
    if (event.source === window && event.data.type === "FETCH_INTERCEPT") {
        console.log("Intercepted fetch response:", event.data);
        // Gửi thông tin này về background hoặc lưu trữ trực tiếp vào local storage
        chrome.runtime.sendMessage({
            action: "dataFetched",
            url: event.data.url,
            method: event.data.method,
            data: event.data.responseBody
        });
    }
});
