body {
    transition: background-color 0.3s, color 0.3s;
}

.content-wrapper {
    padding-top: 60px;
    display: flex;
}

#toastMsg {
    min-width: 250px;
    background-color: #5b5b5b;
    color: #3d97ee;
    text-align: center;
    border-radius: 5px;
    padding: 10px;
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 9999;
    transition: opacity 0.5s, visibility 0.5s;
    opacity: 0.9;
}

 #welcomeHeader,
 #serverSelection,
 #environmentSelection {
     cursor: pointer;
 }


/* Dark mode styles */
body.dark-mode {
    background-color: #1a1a1a;
    color: #ffffff;
}

body.dark-mode .navbar {
    background-color: #2d2d2d !important;
}

body.dark-mode .card {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: #333;
    border-color: #404040;
    color: #999;
}

body.dark-mode .tool-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255,255,255,0.1);
}

body.dark-mode .text-muted {
    color: #999 !important;
}

body.dark-mode .content-area {
    background-color: #1a1a1a;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/*Dark mode for Scrollbar*/
body.dark-mode .card-container::-webkit-scrollbar-track {
    background: #1a1a1a;
}

body.dark-mode .card-container::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

body.dark-mode .card-container::-webkit-scrollbar-thumb:hover {
    background: #bbb;
}

/*Dark mode for Placeholder*/
body.dark-mode ::placeholder {
    color: #999;
}

/* Dark mode for report content */
body.dark-mode .ui-bugs-group-header {
    background-color: #212121;
}

body.dark-mode .list-group-item {
    background-color: #1a1a1a;
    border-color: #404040;
    color: #bbb;
}

/* Dark mode for estimator */
body.dark-mode .estimator-formula {
    color: #aaa;
}

body.dark-mode .estimator-formula:hover {
    color: #333;
    background-color: #bbb;
}


/* Light mode styles */
.navbar {
    background-color: #dee2e6;
}

/* Sidebar styles */
.sidebar {
    min-width: 375px;
    max-width: 375px;
    height: calc(100vh - 70px);
    overflow: auto;
    transition: all 0.3s;
    position: fixed;
}

.sidebar.collapsed {
    min-width: 78px;
    max-width: 78px;
}

.sidebar.collapsed .sidebar-title,
.sidebar.collapsed .menu-to-hide {
    display: none;
}

.sidebar-header {
    border-bottom: 1px solid #dee2e6;
}

.tool-block {
    transition: transform 0.1s, box-shadow 0.1s;
    cursor: pointer;
}

.tool-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.content-area {
    min-height: calc(100vh - 70px);
    background-color: #f8f9fa;
    padding-left: 375px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    width: 100%;
    transition: all 0.3s;
}

.content-area.expanded {
    padding-left: 80px;
}

body.content-render {
    transition: all 1s;
}

.form-select {
    max-width: 110px;
}

/*Scrollbar*/
.card-container {
    max-height: 65vh;
    padding-right: 10px;
}

.card-container::-webkit-scrollbar {
    width: 4px;
}

.card-container::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.card-container::-webkit-scrollbar-thumb {
    background: #bbb;
    border-radius: 4px;
}

.card-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* custom styles for estimator */
.estimator-formula {
    color: #333;
}

.custom-tooltip {
    --bs-tooltip-bg: green;
    --bs-tooltip-color: white;
}

/* Style for Datatable and jQuery */
#comparisonTable_filter {
    margin-bottom: 8px;
}

.purple {
    color: purple;
}

.red {
    color: red;
}

.blue {
    color: blue;
}