<div id="estimatorFragment" class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Digitization Effort Estimator</h4>
                </div>
                <div class="card-body">
                    <!-- Configuration Section -->
                    <div class="mb-4">
                        <h5 class="card-title mb-4">Configuration</h5>
                        <form id="estimatorForm" class="row g-3 justify-content-center">
                            <div class="row">

                                <div class="my-2">
                                    <button class="d-flex justify-content-between btn btn-outline-secondary mb-2 text-start w-100 estimator-formula" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target=".ui-build-multi-collapse"
                                            aria-expanded="false"
                                            aria-controls="firstUiBuildTimeFactor secondUiBuildTimeFactor"
                                            data-bs-placement="top"
                                            data-bs-custom-class="custom-tooltip"
                                            title="Click to change the 'UI Build Factor 1' and 'UI Build Factor 2' if needed.">
                                        <span>
                                            UI Build Time = (<strong><em>UI Build Factor 1</em></strong> x Number of Logic Pages
                                            + <strong><em>UI Build Factor 2</em></strong> x Number of Options) x 0.85
                                        </span>
                                        <i class="bi bi-chevron-down"></i>
                                    </button>

                                    <div class="d-flex">
                                        <div class="col-md-6 pe-2 me-2 collapse ui-build-multi-collapse">
                                            <label for="firstUiBuildTimeFactor" class="form-label">UI Build Factor 1:</label>
                                            <input type="number" class="form-control"
                                                   id="firstUiBuildTimeFactor"
                                                   name="firstUiBuildTimeFactor"
                                                   value="0.2003" required>
                                        </div>
                                        <div class="col-md-6 pe-2 me-2 collapse ui-build-multi-collapse">
                                            <label for="secondUiBuildTimeFactor" class="form-label">UI Build Factor 2:</label>
                                            <input type="number" class="form-control"
                                                   id="secondUiBuildTimeFactor"
                                                   name="secondUiBuildTimeFactor"
                                                   value="0.0263" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="my-2">
                                    <button class="d-flex justify-content-between btn btn-outline-secondary col-md-12 mb-2 text-start estimator-formula" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target=".logic-implementation-multi-collapse"
                                            aria-expanded="false"
                                            aria-controls="firstLogicImplementationTimeFactor secondLogicImplementationTimeFactor"
                                            data-bs-placement="top"
                                            data-bs-custom-class="custom-tooltip"
                                            title="Click to change the 'Logic Implementation Factor 1' and 'Logic Implementation Factor 2' if needed.">
                                        <span>
                                            Logic Implementation Time = (<strong><em>Logic Implementation Factor 1</em></strong> x Number of Logic Pages
                                            + <strong><em>Logic Implementation Factor 2</em></strong> x Number of Options) x 0.8
                                        </span>
                                        <i class="bi bi-chevron-down"></i>
                                    </button>

                                    <div class="d-flex">
                                        <div class="col-md-6 pe-2 me-2 collapse logic-implementation-multi-collapse">
                                            <label for="firstLogicImplementationTimeFactor" class="form-label">Logic Implementation Factor 1:</label>
                                            <input type="number" class="form-control"
                                                   id="firstLogicImplementationTimeFactor"
                                                   name="firstLogicImplementationTimeFactor"
                                                   value="0.0213" required>
                                        </div>
                                        <div class="col-md-6 pe-2 me-2 collapse logic-implementation-multi-collapse">
                                            <label for="secondLogicImplementationTimeFactor" class="form-label">Logic Implementation Factor 2:</label>
                                            <input type="number" class="form-control"
                                                   id="secondLogicImplementationTimeFactor"
                                                   name="secondLogicImplementationTimeFactor"
                                                   value="0.0432" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="my-2">
                                    <button class="d-flex justify-content-between btn btn-outline-secondary col-md-12 mb-2 text-start estimator-formula" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target=".testing-multi-collapse"
                                            aria-expanded="false"
                                            aria-controls="firstTestingTimeFactor secondTestingTimeFactor"
                                            data-bs-placement="top"
                                            data-bs-custom-class="custom-tooltip"
                                            title="Click to change the 'Testing Factor 1' and 'Testing Factor 2' if needed.">
                                        <span>
                                            Testing Time = (<strong><em>Testing Factor 1</em></strong> x Number of Logic Pages
                                            + <strong><em>Testing Factor 2</em></strong> x Number of Options) x 0.85
                                        </span>
                                        <i class="bi bi-chevron-down"></i>
                                    </button>

                                    <div class="d-flex">
                                        <div class="col-md-6 pe-2 me-2 collapse testing-multi-collapse">
                                            <label for="firstTestingTimeFactor" class="form-label">Testing Factor 1:</label>
                                            <input type="number" class="form-control"
                                                   id="firstTestingTimeFactor"
                                                   name="firstTestingTimeFactor"
                                                   value="0.1593" required>
                                        </div>
                                        <div class="col-md-6 pe-2 me-2 collapse testing-multi-collapse">
                                            <label for="secondTestingTimeFactor" class="form-label">Testing Factor 2:</label>
                                            <input type="number" class="form-control"
                                                   id="secondTestingTimeFactor"
                                                   name="secondTestingTimeFactor"
                                                   value="0.0571" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="my-2">
                                    <label for="estimatorFileInput" class="form-label">Upload annotated PDF JSON File(s):</label>
                                    <div class="input-group">
                                        <input type="file" class="form-control" id="estimatorFileInput"
                                               name="files[]" multiple accept=".json">
                                    </div>
                                </div>

                                <div class="my-2 text-center text-danger">
                                    <strong>⚠️ Disclaimer:</strong> This estimator is a prototype and may <strong>NOT</strong> be accurate!
                                    The error range is maybe upto 30%. Please carefully review the results. ⚠️
                                </div>

                                <button type="submit" class="btn btn-primary mt-2 w-25 mx-auto d-block" id="estimatorCalculateButton">
                                    Calculate
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div id="estimatorResultsContent" class="results-content w-100 card my-4 mx-0 px-0" style="display: none;">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Estimation Results</h4>
                </div>
                <div class="card-body">
                    <div class="row table-responsive">
                        <table id="estimatorResultsTable" class="table table-striped table-bordered table-hover">
                            <thead class="thead-dark text-center">
                            <tr>
                                <th>File Name</th>
                                <th>Number of Logic Pages</th>
                                <th>Number of Options (Radio+Checkbox)</th>
                                <th>UI Build Time</th>
                                <th>Logic Implementation Time</th>
                                <th>Testing Time</th>
                            </tr>
                            </thead>
                            <tbody id="estimatorResultsBody">
                            <tr>
                                <td colspan="6" class="text-center">No results available.</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
