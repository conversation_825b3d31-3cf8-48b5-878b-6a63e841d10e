<div class="container-fluid" id="formToBlueprintFragment">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Form To Blueprint Metadata</h4>
                </div>
                <div class="card-body">
                    <div class="my-2 text-center text-danger">
                        <strong>⚠️ NOTE:</strong> Please SAVE with your <strong>DRAFT</strong> version first if any.
                        The Transfer process will create a new Blueprint Version based on the latest version, the DRAFT version will be overwritten.  ⚠️
                    </div>

                    <!-- Configuration Section -->
                    <div class="mb-4">
                        <h5 class="card-title mb-4">Configuration</h5>
                        <form class="row g-3 justify-content-center" id="configForm">
                            <div class="row">
                                <div class="d-flex col-md-12 mt-2">
                                    <div class="col-md-6 me-2">
                                        <label class="form-label" for="retroFormVersionId">Form Version:</label>
                                        <input class="form-control" id="retroFormVersionId" name="retroFormVersionId"
                                               placeholder="e.g.: forp4ryqyyql4zvw6nok.fve0ne1e6v"
                                               required type="text">
                                        <span class="text-danger" id="retroFormVersionIdError"></span>
                                    </div>
                                </div>

                                <div class="d-flex my-3">
                                    <div class="col-md-6 me-2">
                                        <label class="form-label" for="blueprintId">Blueprint Id:</label>
                                        <input class="form-control" id="blueprintId" name="blueprintId" placeholder="e.g.: forp4ryqyyql4zvw6nok.fve0ne1e6v"
                                               required type="text">
                                        <span class="text-danger" id="blueprintIdError"></span>
                                    </div>
                                </div>

                                <div class="d-flex my-3">
                                    <div class="col-md-6 me-2">
                                        <label class="form-label" for="widgetType">Widget type: </label>
                                        <!-- Example single danger button -->
                                        <div class="btn-group">
                                            <button aria-expanded="false" class="btn btn-danger dropdown-toggle"
                                                    data-bs-toggle="dropdown" id="widgetTypeButton"
                                                    type="button">
                                                Widget type
                                            </button>
                                            <ul class="dropdown-menu" id="widgetSelection">
                                                <li><a class="dropdown-item" data-value="FILE_GROUP" href="#">File
                                                    groups</a></li>
                                                <li><a class="dropdown-item" data-value="SIGNATURE"
                                                       href="#">Signatures</a></li>
                                                <li><a class="dropdown-item" data-value="GATING_QUESTION" href="#">Gating
                                                    Questions</a></li>
                                                <li><a class="dropdown-item" data-value="WELL" href="#">Wells</a></li>
                                            </ul>
                                        </div>
                                        <input id="widgetType" name="widgetType" type="hidden">
                                        <span class="text-danger" id="widgetTypeError"></span>
                                    </div>
                                </div>
                                <div class="d-flex my-3">
                                    <div class="col-md-6 me-2">
                                        <label class="form-label" for="aliases">
                                            Aliases (Leave empty if you want to transfer all the fields. Input alias of group if you want to transfer all the fields in the group):
                                        </label>
                                        <input class="form-control" id="aliases" name="aliases" placeholder="e.g.: alias1;alias2"
                                               required type="text">
                                        <span class="text-danger" id="aliasesError"></span>
                                    </div>
                                </div>


                                <button class="btn btn-primary mt-3 w-25 mx-auto d-block" id="formToBlueprintButton"
                                        type="submit">
                                    Transfer
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="card results-content mt-4" id="formToBlueprintResultsContent" style="display: none;"></div>
        </div>
    </div>
</div>
