import {initializeThemeToggle} from './modules/themeToggle.js';
import {syncInputWithLocalStorage} from './utils/localStorageHandler.js';
import EstimatorManager from './classes/estimatorManager';
import UIBugsDetectorManager from './classes/uIBugsDetectorManager';
import FontMismatchDetectorManager from './classes/fontMismatchDetectorManager';
import FormToBlueprintManager from './classes/formToBlueprintManager';
import {showToolUi} from './utils/commonHelpers.js';
import {DOM_LOCATORS} from './constants/domLocators.js';
import {LOCAL_STORAGE_KEYS} from './constants/localStorageKeys.js';

// Load templates: add "?raw" at the end of the file path, Vite will handle it as a string, no need any extension
// See: https://vite.dev/guide/assets.html#importing-asset-as-string
import welcomeFragment from '/src/components/welcome.html?raw';
import estimatorFragment from '/src/components/estimator.html?raw';
import uiBugsFragment from '/src/components/uiBugsDetector.html?raw';
import compareMappingWidgetsFormToFormFragment from '/src/components/compareMappingWidgetsFormToForm.html?raw';
import fontMismatchDetectorFragment from '/src/components/fontMismatchDetector.html?raw';
import CompareMappingWidgetsFormToFormManager from './classes/compareMappingWidgetsFormToFormManager';
import formToBlueprintFragment from '/src/components/formToBlueprint.html?raw';

const sidebarToggleBtn = document.querySelector(DOM_LOCATORS.SIDEBAR_TOGGLE);
const sidebar = document.getElementById(DOM_LOCATORS.SIDEBAR);
const bearerTokenInput = sidebar.querySelector(DOM_LOCATORS.BEARER_TOKEN_INPUT);
const contentArea = document.getElementById(DOM_LOCATORS.CONTENT_AREA);
const welcomeHeader = document.getElementById(DOM_LOCATORS.WELCOME_HEADER);
const welcomeContent = document.getElementById(DOM_LOCATORS.WELCOME_CONTENT);
const estimatorContent = document.getElementById(DOM_LOCATORS.ESTIMATOR_CONTENT);
const estimatorCard = document.getElementById(DOM_LOCATORS.ESTIMATOR_CARD);
const uiBugsDetectorContent = document.getElementById(DOM_LOCATORS.UI_BUGS_DETECTOR_CONTENT);
const uiBugsDetectorCard = document.getElementById(DOM_LOCATORS.UI_BUGS_DETECTOR_CARD);
const compareMappingWidgetsFormToFormContent = document.getElementById(DOM_LOCATORS.COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_CONTENT);
const compareMappingWidgetsFormToFormCard = document.getElementById(DOM_LOCATORS.COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_CARD);
const fontMismatchDetectorContent = document.getElementById(DOM_LOCATORS.FONT_MISMATCH_DETECTOR_CONTENT);
const fontMismatchDetectorCard = document.getElementById(DOM_LOCATORS.FONT_MISMATCH_DETECTOR_CARD);
const formToBlueprintCard = document.getElementById(DOM_LOCATORS.FORM_TO_BLUEPRINT_CARD);
const formToBlueprintContent = document.getElementById(DOM_LOCATORS.FORM_TO_BLUEPRINT_CONTENT);


const templates = {
    welcomeFragment: welcomeFragment,
    estimatorFragment: estimatorFragment,
    uiBugsFragment: uiBugsFragment,
    compareMappingWidgetsFormToFormFragment: compareMappingWidgetsFormToFormFragment,
    fontMismatchDetectorFragment: fontMismatchDetectorFragment,
    formToBlueprintFragment: formToBlueprintFragment,
};

function renderTemplate(templateName, templateContentId) {
    const content = document.getElementById(templateContentId);
    content.innerHTML = templates[templateName];
}

function welcomeSupport() {
    if (welcomeContent) showToolUi(welcomeContent);
}

function estimatorSupport() {
    if (estimatorContent) showToolUi(estimatorContent);
}

function uiBugsDetectorSupport() {
    if (uiBugsDetectorContent) showToolUi(uiBugsDetectorContent);

    const formVersionIdInput = uiBugsDetectorContent.querySelector(`#${DOM_LOCATORS.TARGET_FORM_VERSION_ID}`);
    const expectedPageSubtitleInput = uiBugsDetectorContent.querySelector(`#${DOM_LOCATORS.EXPECTED_PAGE_SUBTITLE}`);
    const embeddedPdfPrefixInput = uiBugsDetectorContent.querySelector(`#${DOM_LOCATORS.EMBEDDED_PDF_PREFIX}`);

    syncInputWithLocalStorage(formVersionIdInput, LOCAL_STORAGE_KEYS.FORM_VERSION);
    syncInputWithLocalStorage(expectedPageSubtitleInput, LOCAL_STORAGE_KEYS.EXPECTED_PAGE_SUBTITLE);
    syncInputWithLocalStorage(embeddedPdfPrefixInput, LOCAL_STORAGE_KEYS.EXPECTED_EMBEDDED_PDF_PREFIX);
}

function compareMappingWidgetsFormToFormSupport() {
    if (compareMappingWidgetsFormToFormContent) showToolUi(compareMappingWidgetsFormToFormContent);

    const sourceFormTemplateMappingVersionInput = compareMappingWidgetsFormToFormContent.querySelector(`#${DOM_LOCATORS.SOURCE_FORM_TEMPLATE_MAPPING_VERSION_ID}`);
    const destinationFormVersionInput = compareMappingWidgetsFormToFormContent.querySelector(`#${DOM_LOCATORS.DESTINATION_FORM_VERSION_ID}`);

    syncInputWithLocalStorage(sourceFormTemplateMappingVersionInput, LOCAL_STORAGE_KEYS.SOURCE_FORM_TEMPLATE_MAPPING_VERSION);
    syncInputWithLocalStorage(destinationFormVersionInput, LOCAL_STORAGE_KEYS.DESTINATION_FORM_VERSION);
}

function fontMismatchDetectorSupport() {
    if (fontMismatchDetectorContent) showToolUi(fontMismatchDetectorContent);

    const targetDocumentVersionInput = fontMismatchDetectorContent.querySelector(`#${DOM_LOCATORS.DOCUMENT_VERSION_ID}`);
    syncInputWithLocalStorage(targetDocumentVersionInput, LOCAL_STORAGE_KEYS.DOCUMENT_VERSION);
}

function formToBlueprintSupport() {
    if (formToBlueprintContent) showToolUi(formToBlueprintContent);

    const formVersionIdInput = formToBlueprintContent.querySelector(`#${DOM_LOCATORS.RETRO_FORM_VERSION_ID}`);
    const blueprintIdInput = formToBlueprintContent.querySelector(`#${DOM_LOCATORS.BLUEPRINT_ID}`);

    syncInputWithLocalStorage(formVersionIdInput, LOCAL_STORAGE_KEYS.FORM_VERSION);
    syncInputWithLocalStorage(blueprintIdInput, LOCAL_STORAGE_KEYS.BLUEPRINT_ID);
}

function initializeEvents() {
    // === SIDEBAR ===
    // Initialize Dark/Light theme toggle
    initializeThemeToggle();

    // Expand/collapse sidebar
    sidebarToggleBtn.addEventListener('click', function () {
        sidebar.classList.toggle('collapsed');
        contentArea.classList.toggle('expanded');
    });

    // Friendly UX: Listen for inputted some field value on UI and save them to local storage
    syncInputWithLocalStorage(bearerTokenInput, LOCAL_STORAGE_KEYS.BEARER_TOKEN);

    // === MAIN CONTENT ===

    // Welcome
    renderTemplate('welcomeFragment', DOM_LOCATORS.WELCOME_CONTENT);
    welcomeHeader.addEventListener('click', welcomeSupport);

    // Estimator
    renderTemplate('estimatorFragment', DOM_LOCATORS.ESTIMATOR_CONTENT);
    const estimatorManager = new EstimatorManager(DOM_LOCATORS.ESTIMATOR_CALCULATE_BUTTON, DOM_LOCATORS.ESTIMATOR_RESULTS_CONTENT);
    estimatorManager.initialize();
    estimatorCard.addEventListener('click', estimatorSupport);

    // UI Bugs Detector
    renderTemplate('uiBugsFragment', DOM_LOCATORS.UI_BUGS_DETECTOR_CONTENT);
    const uiBugsDetectorManager = new UIBugsDetectorManager(DOM_LOCATORS.UI_BUGS_ANALYZE_BUTTON, DOM_LOCATORS.UI_BUGS_DETECTOR_RESULTS_CONTENT);
    uiBugsDetectorManager.initialize();
    uiBugsDetectorCard.addEventListener('click', uiBugsDetectorSupport);

    // Compare Mapping Widgets Form To Form for exporting the Form Export Mappings
    renderTemplate('compareMappingWidgetsFormToFormFragment', DOM_LOCATORS.COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_CONTENT);
    const compareMappingWidgetsFormToFormManager = new CompareMappingWidgetsFormToFormManager(
        DOM_LOCATORS.COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_COMPARE_BUTTON,
        DOM_LOCATORS.COMPARE_MAPPING_WIDGETS_FORM_TO_FORM_RESULTS_CONTENT
    );
    compareMappingWidgetsFormToFormManager.initialize();
    compareMappingWidgetsFormToFormCard.addEventListener('click', compareMappingWidgetsFormToFormSupport);

    // Font Mismatch Detector
    renderTemplate('fontMismatchDetectorFragment', DOM_LOCATORS.FONT_MISMATCH_DETECTOR_CONTENT);
    const fontMismatchDetectorManager = new FontMismatchDetectorManager(
        DOM_LOCATORS.FIND_MISMATCHES_BTN,
        DOM_LOCATORS.FONT_MISMATCH_DETECTOR_RESULTS_CONTENT
    );
    fontMismatchDetectorManager.initialize();
    fontMismatchDetectorCard.addEventListener('click', fontMismatchDetectorSupport);

    // Form to blueprint
    renderTemplate('formToBlueprintFragment', DOM_LOCATORS.FORM_TO_BLUEPRINT_CONTENT);
    const formToBlueprintManager = new FormToBlueprintManager(
        DOM_LOCATORS.FORM_TO_BLUEPRINT_BUTTON,
        DOM_LOCATORS.FORM_TO_BLUEPRINT_RESULTS_CONTENT
    );
    formToBlueprintManager.initialize();
    formToBlueprintCard.addEventListener('click', formToBlueprintSupport);

}

document.addEventListener('DOMContentLoaded', initializeEvents);
