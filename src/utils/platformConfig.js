export const PLATFORM_CONFIG = {
    scheme: 'https',
    subDomain: 'portal',
    baseDomain: 'anduin',
    getFormEndpoint: '/api/v3/form/getForm',
    getDocumentEndpoint: '/api/v3/annotationDocument/getDocument',
    createOrEditFileGroupEndpoint: '/api/v3/blueprint/createOrEditFileGroup',
    createOrEditBlueprintSignatureEndpoint: '/api/v3/blueprint/createOrEditBlueprintSignature',
    createOrEditGatingQuestionEndpoint: '/api/v3/blueprint/createOrEditGatingQuestion',
    createOrEditWellEndpoint: '/api/v3/blueprint/createOrEditParagraph',
    createBlueprintVersion: '/api/v3/blueprint/createBlueprintVersion',
    getBlueprintVersion: '/api/v3/blueprint/getBlueprintVersionCatalaCode',
    getBlueprint: '/api/v3/blueprint/getBlueprint',
    getMappingVersionEndpoint: '/api/v3/formTemplateMapping/getMappingVersion',
};

export function getUrlUsage(serverSelection, environmentSelection, topLevelDomainSelection) {
    const server = serverSelection === 'EU' ? '.eu' : '';
    const environment = environmentSelection === 'Minas' ? '-minas-tirith' : '';
    const topLevelDomain = topLevelDomainSelection === 'Minas' ? 'dev' : 'app';

    return `${PLATFORM_CONFIG.scheme}://${PLATFORM_CONFIG.subDomain}${environment}${server}.${PLATFORM_CONFIG.baseDomain}.${topLevelDomain}`;
}
