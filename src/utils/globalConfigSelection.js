import {getValueFromLocalStorage} from './localStorageHandler';
import { DOM_LOCATORS } from '../constants/domLocators';

export const getGlobalConfigSelection = () => ({
    serverSelection: document.getElementById('serverSelection').value,
    environmentSelection: document.getElementById('environmentSelection').value,
    topLevelDomainSelection: document.getElementById('environmentSelection').value,
    token: getValueFromLocalStorage('bearerToken'),
});
