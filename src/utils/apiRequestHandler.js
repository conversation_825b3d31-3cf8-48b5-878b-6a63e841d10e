import {getUrlUsage, PLATFORM_CONFIG} from './platformConfig';
import {getGlobalConfigSelection} from './globalConfigSelection';

const getBaseConfig = () => ({
    serverSelection: getGlobalConfigSelection().serverSelection,
    environmentSelection: getGlobalConfigSelection().environmentSelection,
    topLevelDomainSelection: getGlobalConfigSelection().topLevelDomainSelection
});

/**
 * Makes a POST request to `/api/v3/form/getForm` in order to retrieve the form specified by the given `formVersion`.
 * @param {string} formVersion - the formVersion to retrieve, in the format <formId>.<versionId>
 * @returns {Promise<Object>} - the response from the API call
 * @throws {Error} - if the request fails for any reason
 */
export async function getForm(formVersion) {
    try {
        const config = getBaseConfig();
        const getFormUrl = getUrlUsage(config.serverSelection, config.environmentSelection, config.topLevelDomainSelection) +
            PLATFORM_CONFIG.getFormEndpoint;

        console.debug(getFormUrl);

        const [formId, versionId] = formVersion.split('.');

        const response = await fetch(getFormUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify({
                formId: formId,
                versionIdOpt: versionId !== undefined ? formVersion : null
            })
        });

        if (!response.ok) {
            console.log(`Network response was not ok: ${response.status} - ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        alert('An error occurred. Please try again.\n\n' + error.message);
    }
}

/**
 * Makes a POST request to `/api/v3/annotationDocument/getDocument` in order to retrieve the document specified by the given `documentVersionId`.
 * @param {string} documentVersionId - the documentVersionId to retrieve, in the format <documentId>.<versionId>
 * @returns {Promise<Object>} - the response from the API call
 * @throws {Error} - if the request fails for any reason
 */
export async function getDocument(documentVersionId) {
    try {
        const config = getBaseConfig();
        const getDocumentUrl = getUrlUsage(config.serverSelection, config.environmentSelection, config.topLevelDomainSelection) +
            PLATFORM_CONFIG.getDocumentEndpoint;

        console.debug(getDocumentUrl);

        const [documentId] = documentVersionId.split('.');

        const response = await fetch(getDocumentUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify({
                documentId: documentId,
                versionIdOpt: documentVersionId
            })
        });

        if (!response.ok) {
            console.log(`Network response was not ok: ${response.status} - ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        alert('An error occurred. Please try again.\n\n' + error.message);
    }
}

/**
 * Makes a POST request to `/api/v3/mappingVersion/getMappingVersion` in order to retrieve the mapping version specified by the given `formTemplateMappingVersionId`.
 * @param {string} formTemplateMappingVersionId - the mapping version to retrieve, in the format <mappingId>.<versionId>
 * @returns {Promise<Object>} - the response from the API call
 * @throws {Error} - if the request fails for any reason
 */
export async function getMappingVersion(formTemplateMappingVersionId) {
    try {
        const config = getBaseConfig();
        const getMappingVersionUrl = getUrlUsage(config.serverSelection, config.environmentSelection, config.topLevelDomainSelection) +
            PLATFORM_CONFIG.getMappingVersionEndpoint;

        console.debug(getMappingVersionUrl);

        const idParts = formTemplateMappingVersionId.split('.');
        let mappingId = null, versionIdOpt = null;

        if (idParts.length === 3) {
            mappingId = formTemplateMappingVersionId;
        } else if (idParts.length === 4) {
            mappingId = idParts.slice(0, 3).join('.');
            versionIdOpt = formTemplateMappingVersionId;
        }

        const response = await fetch(getMappingVersionUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify({
                mappingId: mappingId,
                versionIdOpt: versionIdOpt
            })
        });

        if (!response.ok) {
            console.log(`Network response was not ok: ${response.status} - ${response.statusText}`);
        }

        return await response.json();

    } catch (error) {
        alert('An error occurred. Please try again.\n\n' + error.message);
    }
}
