export function setValueToLocalStorage(key, value) {
    try {
        localStorage.setItem(key, value);
    } catch (error) {
        console.error('Error setting value to localStorage:', error);
    }
}

export function getValueFromLocalStorage(key) {
    try {
        return localStorage.getItem(key);
    } catch (error) {
        console.error('Error getting value from localStorage:', error);
        return null;
    }
}

export function syncInputWithLocalStorage(inputElement, storageKey) {
    if (!inputElement) return;

    const storedValue = getValueFromLocalStorage(storageKey);
    if (storedValue) inputElement.value = storedValue;

    inputElement.addEventListener('input', (event) => {
        const newValue = event.target.value.trim();
        setValueToLocalStorage(storageKey, newValue);
    })
}
