// Function to create or edit a FileGroup
import {getUrlUsage, PLATFORM_CONFIG} from './platformConfig';
import {getGlobalConfigSelection} from './globalConfigSelection';

export async function createOrEditFileGroup(fileGroupData) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.createOrEditFileGroupEndpoint;

        console.debug(`Creating/Editing FileGroup for path: ${fileGroupData.path}`);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(fileGroupData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error(`Error creating/editing FileGroup for path: ${fileGroupData.path}:`, error);
        throw error;
    }
}


export async function createOrEditBlueprintSignature(signatureData) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.createOrEditBlueprintSignatureEndpoint;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(signatureData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error(`Error creating/editing FileGroup for path: ${signatureData.path}:`, error);
        throw error;
    }
}

export async function createOrEditGatingQuestion(gatingQuestionData) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.createOrEditGatingQuestionEndpoint;

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(gatingQuestionData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`Signature created/edited successfully`);
        return data;
    } catch (error) {
        console.error(`Error creating/editing FileGroup for ${gatingQuestionData}:`, error);
        throw error;
    }
}
export async function createOrEditWell(wellData) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.createOrEditWellEndpoint;

        console.debug(url);

        //move to preprocess
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(wellData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log(`Well created/edited successfully`);
        return data;
    } catch (error) {
        throw error;
    }
}

export async function createBlueprintVersion(blueprintData) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.createBlueprintVersion;

        console.debug(url);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(blueprintData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.debug(`Blueprint saved successfully`);
        return data;
    } catch (error) {
        console.error(`Error creating/editing Blueprint`, error);
        throw error;
    }
}

export async function getBlueprint(blueprintId) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.getBlueprint;

        console.debug(url);

        //move to preprocess
        const payload = { blueprintId : blueprintId};
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

export async function getBlueprintCatalaVersion(blueprintVersionId) {
    try {

        const url =
            getUrlUsage(
                getGlobalConfigSelection().serverSelection,
                getGlobalConfigSelection().environmentSelection,
                getGlobalConfigSelection().topLevelDomainSelection
            )
            + PLATFORM_CONFIG.getBlueprintVersion;

        console.debug(url);

        //move to preprocess
        const payload = { blueprintVersionId : blueprintVersionId};
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getGlobalConfigSelection().token}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        return data;
    } catch (error) {
        throw error;
    }
}

