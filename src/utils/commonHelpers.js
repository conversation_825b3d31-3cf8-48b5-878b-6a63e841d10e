import { DOM_LOCATORS, CSS_CLASSES } from '../constants/domLocators';

/**
 * Shows the toast message for 2 seconds.
 *
 * This function is supposed to be used with {@link copyToClipboardByClass} to
 * show the user that the text has been copied to the clipboard.
 */
export function showToast() {
    const toastMsg = document.getElementById(DOM_LOCATORS.TOAST_MSG);
    toastMsg.style.display = 'block';

    setTimeout(() => {
        toastMsg.style.display = 'none';
    }, 2000);
}

/**
 * Copies the given text to the user's clipboard.
 * @param {string} text The text to copy.
 */
export function copyToClipboard(text) {
    navigator.clipboard.writeText(text)
        .then(showToast)
        .catch((error) => console.error("⛔️ Clipboard copy failed", error));
}

/**
 * Listens for double-click events on elements with the given CSS class name.
 * When such an event is detected, the element's text content is copied to the
 * user's clipboard.
 *
 * @param {string} className The CSS class name to listen for.
 */
export function copyToClipboardByClass(className) {
    document.addEventListener('dblclick', (event) => {
        if (event.target.classList.contains(className)) {
            const text = event.target.textContent;
            copyToClipboard(text);
        }
    });
}

/**
 * Toggles the chevron icon on each HTML group header.
 *
 * When expanding or collapsing a group, the chevron icon
 * should be toggled between `.bi-chevron-down` and `.bi-chevron-up`.
 *
 * @param {HTMLElement} resultsContent - The HTML element ID containing the chevron icon
 */
export function changeChevronIcon(resultsContent) {
    resultsContent.querySelectorAll('[data-bs-toggle="collapse"]').forEach(header => {
        header.addEventListener('click', () => {
            const chevron = header.querySelector(`.${CSS_CLASSES.CHEVRON_DOWN}, .${CSS_CLASSES.CHEVRON_UP}`);
            chevron.classList.toggle(CSS_CLASSES.CHEVRON_UP);
            chevron.classList.toggle(CSS_CLASSES.CHEVRON_DOWN);
        });
    });
}

/**
 * Shows a specific tool UI and hides all other tools.
 *
 * @param {HTMLElement} toolId - The HTML element ID to show
 */
export function showToolUi(toolId) {
    const tools = document.querySelectorAll(`.${CSS_CLASSES.CONTENT_RENDER}`);
    tools.forEach(tool => {
        tool.style.display = 'none';
    });

    if (toolId) {
        toolId.style.display = 'block';
    } else {
        console.warn(`Tool with ID "${toolId}" does not exist.`);
    }
}

/**
 * Show tooltips using the Bootstrap library
 */
export function showBoostStrapTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Destroys an existing DataTable.
 *
 * This function takes a DataTable ID as an argument, checks if it exists,
 * and if so, destroys it. This is useful when re-populating a DataTable
 * with new data.
 *
 * @param {string} tableResultId - The HTML element ID of the DataTable
 */
export function destroyExistingDataTable(tableResultId) {
    if ($.fn.DataTable.isDataTable(`#${tableResultId}`)) {
        $(`#${tableResultId}`).DataTable().destroy();
    }
}

/**
 * Using the DataTables library, this function toggles the filter on a DataTable.
 *
 * @param tableResultId - The HTML element ID of the DataTable
 * @param filterBtnId - The HTML element ID of the filter button
 * @param compareValue - The value to compare
 */
export function toggleFilterOnDataTable(tableResultId, filterBtnId, compareValue) {
    let filterApplied = false;
    const table = $(`#${tableResultId}`).DataTable({
        scrollX: true,
        pageLength: 50,
    });

    // Reset filter state when table is recreated
    $.fn.dataTable.ext.search = [];

    $(`#${filterBtnId}`).on('click', function () {
        if (!filterApplied) {
            $.fn.dataTable.ext.search.push(
                function (settings, data, dataIndex) {
                    for (let i = 0; i < data.length; i++) {
                        if (data[i] === compareValue) {
                            return true;
                        }
                    }
                    return false;
                }
            );
            filterApplied = true;
        } else {
            $.fn.dataTable.ext.search.pop();
            filterApplied = false;
        }
        table.draw();
    });
}

// TODO: Enhance to get multiple alias pairs
/**
 * Parses a logic object to extract alias pairs based on the `mappingItems` object.
 * This function is particularly useful for handling data obtained from the `/getMappingVersions` endpoint.
 *
 * @param {Object} mappingItems - The mapping items object obtained from the `/getMappingVersions` endpoint.
 * @returns {Promise<Array>} - A promise that resolves to an array of alias pairs extracted from the logic object.
 */
export function parseLogicToAliasesPairsFromMappingItems(mappingItems) {
    const repeatableNamePattern = /function\s*\(\s*([\s\S]repeatable[\s\S]*?)\s*\)/;
    const repeatableChildPattern = /.*?\.value\[0\]\.([\s\S]*?)\.value*?/;
    const functionPattern = /function\s*\(\s*([\s\S]*?)\s*\)/;
    const outputAliasPattern = /atd\.add\s*\(\s*"([\s\S]*?)"/;

    return mappingItems.map(item => {
        const isRepeatable = item.logic.match(repeatableNamePattern) || item.logic.match(repeatableChildPattern);
        const inputAliasInRepeatableMatch = item.logic.match(repeatableChildPattern);
        const inputAliasMatch = item.logic.match(functionPattern);
        const inputAlias = isRepeatable ? inputAliasInRepeatableMatch?.[1] : inputAliasMatch ? inputAliasMatch?.[1] : '';

        const outputAliasMatches = item.logic.match(outputAliasPattern);
        const outputAlias = outputAliasMatches ? outputAliasMatches[1] : '';

        return {
            alias1: inputAlias,
            alias2: outputAlias
        };
    });
}

/**
 * Finds all property names under a specific object name in the schema
 * @param {Object} schema - The schema object to search through
 * @param {string} targetName - The name of the object to search under
 * @param {string} targetType - The type of properties to find
 * @returns {string[]} Array of all property names found under the target object
 */
export function findPropertyNamesUnderObject(schema, targetName, targetType) {
    const propertyNames = [];

    function traverse(obj) {
        // If obj is not an object or is null, return
        if (!obj || typeof obj !== 'object') {
            return;
        }

        // If this is the target object we're looking for
        if (obj.name === targetName && obj.type === 'object' && Array.isArray(obj.properties)) {
            // Process all properties recursively
            processProperties(obj.properties);
        }

        // Recursively traverse all object properties
        Object.values(obj).forEach(value => {
            if (typeof value === 'object' && value !== null) {
                traverse(value);
            }
        });
    }

    function processProperties(properties) {
        if (!Array.isArray(properties)) return;
        
        properties.forEach(prop => {
            if (!prop) return;
            
            // If the property matches the target type, add it to the results
            if (!targetType || prop.type === targetType) {
                propertyNames.push(prop.name);
            }
            
            // If the property is an object type, recursively process its properties
            if (prop.type === 'object' && Array.isArray(prop.properties)) {
                processProperties(prop.properties);
            }
        });
    }

    traverse(schema);
    return propertyNames;
}
