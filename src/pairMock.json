[{"alias1": "group1", "alias2": "group1"}, {"alias1": "fundselection1", "alias2": "fundselection1"}, {"alias1": "radio1", "alias2": "radio1"}, {"alias1": "radio2", "alias2": "radio2"}, {"alias1": "individual_investortype", "alias2": "individual_investortype"}, {"alias1": "entity_investortype", "alias2": "entity_investortype"}, {"alias1": "radio15", "alias2": "radio15"}, {"alias1": "group62", "alias2": "group62"}, {"alias1": "repsinsection8garenottrue_checkapplicable1", "alias2": "repsinsection8garenottrue_checkapplicable1"}, {"alias1": "considerbeneficialowner_ifcheckany3boxesofsection8g1", "alias2": "considerbeneficialowner_ifcheckany3boxesofsection8g1"}, {"alias1": "numberofbeneficialowner_section8g4nottrue1", "alias2": "numberofbeneficialowner_section8g4nottrue1"}, {"alias1": "firstname", "alias2": "firstname"}, {"alias1": "middlename", "alias2": "middlename"}, {"alias1": "lastname", "alias2": "lastname"}, {"alias1": "suffix", "alias2": "suffix"}, {"alias1": "mergedname", "alias2": "mergedname"}, {"alias1": "jointname", "alias2": "jointname"}, {"alias1": "investorname_entity", "alias2": "investorname_entity"}, {"alias1": "initials", "alias2": "initials"}, {"alias1": "accounttitle_contactinfo", "alias2": "accounttitle_contactinfo"}, {"alias1": "asa_tin_generalinfo22", "alias2": "asa_tin_generalinfo22"}, {"alias1": "ssn22", "alias2": "ssn22"}, {"alias1": "ein22", "alias2": "ein22"}, {"alias1": "itin22", "alias2": "itin22"}, {"alias1": "textbox39", "alias2": "textbox39"}, {"alias1": "country", "alias2": "country"}, {"alias1": "numberandstreet", "alias2": "numberandstreet"}, {"alias1": "city", "alias2": "city"}, {"alias1": "usstate", "alias2": "usstate"}, {"alias1": "uszipcode", "alias2": "uszipcode"}, {"alias1": "state", "alias2": "state"}, {"alias1": "zipcode", "alias2": "zipcode"}, {"alias1": "combined_address", "alias2": "combined_address"}, {"alias1": "namecontactperson", "alias2": "namecontactperson"}, {"alias1": "textbox", "alias2": "textbox"}, {"alias1": "textbox20", "alias2": "textbox20"}, {"alias1": "country_primarycontact_contactinfo", "alias2": "country_primarycontact_contactinfo"}, {"alias1": "street_primarycontact_contactinfo", "alias2": "street_primarycontact_contactinfo"}, {"alias1": "city_primarycontact_contactinfo", "alias2": "city_primarycontact_contactinfo"}, {"alias1": "usstate_primarycontact_contactinfo", "alias2": "usstate_primarycontact_contactinfo"}, {"alias1": "uszip_primarycontact_generalinfo", "alias2": "uszip_primarycontact_generalinfo"}, {"alias1": "nonusstate_primarycontact_contactinfo", "alias2": "nonusstate_primarycontact_contactinfo"}, {"alias1": "nonuszip_primarycontact_generalinfo", "alias2": "nonuszip_primarycontact_generalinfo"}, {"alias1": "fulladdress_primarycontact_contactinfo", "alias2": "fulladdress_primarycontact_contactinfo"}, {"alias1": "telephonecontactperson", "alias2": "telephonecontactperson"}, {"alias1": "emailcontactperson", "alias2": "emailcontactperson"}, {"alias1": "asa_correspondences_primarycontact_contactinfo1", "alias2": "asa_correspondences_primarycontact_contactinfo1"}, {"alias1": "repeatable", "alias2": "repeatable"}, {"alias1": "cfpeupdatecheckbox_contactinfo", "alias2": "cfpeupdatecheckbox_contactinfo"}, {"alias1": "investorname_wireinfo", "alias2": "investorname_wireinfo"}, {"alias1": "parnershipname_wireinfo", "alias2": "parnershipname_wireinfo"}, {"alias1": "dropdown", "alias2": "dropdown"}, {"alias1": "integer", "alias2": "integer"}, {"alias1": "taxyearend_wireinfo", "alias2": "taxyearend_wireinfo"}, {"alias1": "asa_tin_generalinfo", "alias2": "asa_tin_generalinfo"}, {"alias1": "ssn", "alias2": "ssn"}, {"alias1": "ein", "alias2": "ein"}, {"alias1": "itin", "alias2": "itin"}, {"alias1": "textbox2", "alias2": "textbox2"}, {"alias1": "date_wireinfo", "alias2": "date_wireinfo"}, {"alias1": "asa_bankname_wireinstructionsto_wireinfo", "alias2": "asa_bankname_wireinstructionsto_wireinfo"}, {"alias1": "radio", "alias2": "radio"}, {"alias1": "abaroutingno_wireinfo", "alias2": "abaroutingno_wireinfo"}, {"alias1": "swift_wireinfo", "alias2": "swift_wireinfo"}, {"alias1": "asa_banklocation_wireinstructionsto_wireinfo", "alias2": "asa_banklocation_wireinstructionsto_wireinfo"}, {"alias1": "asa_accountnumber_wireinstructionsto_wireinfo", "alias2": "asa_accountnumber_wireinstructionsto_wireinfo"}, {"alias1": "asa_accountname_wireinstructionsto_wireinfo", "alias2": "asa_accountname_wireinstructionsto_wireinfo"}, {"alias1": "specialinstruction_wireinfo", "alias2": "specialinstruction_wireinfo"}, {"alias1": "asa_furthercreditname_wireinstructionsto_wireinfo", "alias2": "asa_furthercreditname_wireinstructionsto_wireinfo"}, {"alias1": "asa_furthercreditaccount_wireinstructionsto_wireinfo", "alias2": "asa_furthercreditaccount_wireinstructionsto_wireinfo"}, {"alias1": "cfpeupdatecheckbox_wireinfo", "alias2": "cfpeupdatecheckbox_wireinfo"}, {"alias1": "usperson_partb_investorquestionnaire", "alias2": "usperson_partb_investorquestionnaire"}, {"alias1": "na_canadianinvestor1", "alias2": "na_canadianinvestor1"}, {"alias1": "na_eeaukswiss2", "alias2": "na_eeaukswiss2"}, {"alias1": "taxyear_partb_investorquestionnaire", "alias2": "taxyear_partb_investorquestionnaire"}, {"alias1": "specify_othertaxableyear_taxyear_partb_investorquestionnaire", "alias2": "specify_othertaxableyear_taxyear_partb_investorquestionnaire"}, {"alias1": "taxexempt_partb_investorquestionnaire", "alias2": "taxexempt_partb_investorquestionnaire"}, {"alias1": "taxexemptbasis_partb_investorquestionnaire", "alias2": "taxexemptbasis_partb_investorquestionnaire"}, {"alias1": "specify_other_taxexemptbasis_partb_investorquestionnaire", "alias2": "specify_other_taxexemptbasis_partb_investorquestionnaire"}, {"alias1": "nonustaxexempt_partb_investorquestionnaire", "alias2": "nonustaxexempt_partb_investorquestionnaire"}, {"alias1": "disregardedentity_partb_investorquestionnaire", "alias2": "disregardedentity_partb_investorquestionnaire"}, {"alias1": "specify_yes_disregardedentity_partb_investorquestionnaire", "alias2": "specify_yes_disregardedentity_partb_investorquestionnaire"}, {"alias1": "individual_accreditedinvestor", "alias2": "individual_accreditedinvestor"}, {"alias1": "trust_accreditedinvestor", "alias2": "trust_accreditedinvestor"}, {"alias1": "grantors_revocable_trust_accreditedinvestor", "alias2": "grantors_revocable_trust_accreditedinvestor"}, {"alias1": "entityothertthantrust_accreditedinvestor", "alias2": "entityothertthantrust_accreditedinvestor"}, {"alias1": "fiduciaryn<PERSON>_asa_yes_erisaplan_accreditedinvestor", "alias2": "fiduciaryn<PERSON>_asa_yes_erisaplan_accreditedinvestor"}, {"alias1": "family_accreditedinvestor", "alias2": "family_accreditedinvestor"}, {"alias1": "own5m_naturalperson_qualifiedpurchaser", "alias2": "own5m_naturalperson_qualifiedpurchaser"}, {"alias1": "entity_qualifiedpurchaser", "alias2": "entity_qualifiedpurchaser"}, {"alias1": "asa_qualifiedclient_qualifiedclient", "alias2": "asa_qualifiedclient_qualifiedclient"}, {"alias1": "notnaturalperson_qualifiedclient", "alias2": "notnaturalperson_qualifiedclient"}, {"alias1": "d_notnaturalperson_qualifiedclient", "alias2": "d_notnaturalperson_qualifiedclient"}, {"alias1": "equityowner_lookthroughentity", "alias2": "equityowner_lookthroughentity"}, {"alias1": "qualifiedclient_lookthroughentity", "alias2": "qualifiedclient_lookthroughentity"}, {"alias1": "notqualifiedclient_lookthroughentity", "alias2": "notqualifiedclient_lookthroughentity"}, {"alias1": "subscriberis_sectionia_finra", "alias2": "subscriberis_sectionia_finra"}, {"alias1": "describe_notrevoked_subscriberis_sectionia_finra", "alias2": "describe_notrevoked_subscriberis_sectionia_finra"}, {"alias1": "noneofabove_sectionib_finra", "alias2": "noneofabove_sectionib_finra"}, {"alias1": "radio3", "alias2": "radio3"}, {"alias1": "subscriberis_sectioniia_finra", "alias2": "subscriberis_sectioniia_finra"}, {"alias1": "a_immidiatefamilymember_subscriberis_sectioniia_finra", "alias2": "a_immidiatefamilymember_subscriberis_sectioniia_finra"}, {"alias1": "b_immidiatefamilymember_subscriberis_sectioniia_finra", "alias2": "b_immidiatefamilymember_subscriberis_sectioniia_finra"}, {"alias1": "clarify_yes_b_immidiatefamilymember_subscriberis_sectioniia_finra", "alias2": "clarify_yes_b_immidiatefamilymember_subscriberis_sectioniia_finra"}, {"alias1": "c_immidiatefamilymember_subscriberis_sectioniia_finra", "alias2": "c_immidiatefamilymember_subscriberis_sectioniia_finra"}, {"alias1": "clarify_yes_c_immidiatefamilymember_subscriberis_sectioniia_finra", "alias2": "clarify_yes_c_immidiatefamilymember_subscriberis_sectioniia_finra"}, {"alias1": "clarify_finder_subscriberis_sectioniia_finra", "alias2": "clarify_finder_subscriberis_sectioniia_finra"}, {"alias1": "clarify_item6_subscriberis_sectioniia_finra", "alias2": "clarify_item6_subscriberis_sectioniia_finra"}, {"alias1": "a_item12_subscriberis_sectioniia_finra", "alias2": "a_item12_subscriberis_sectioniia_finra"}, {"alias1": "b_item12_subscriberis_sectioniia_finra", "alias2": "b_item12_subscriberis_sectioniia_finra"}, {"alias1": "c_item12_subscriberis_sectioniia_finra", "alias2": "c_item12_subscriberis_sectioniia_finra"}, {"alias1": "clarify_yes_c_item12_subscriberis_sectioniia_finra", "alias2": "clarify_yes_c_item12_subscriberis_sectioniia_finra"}, {"alias1": "noneofabove_sectioniib_finra", "alias2": "noneofabove_sectioniib_finra"}, {"alias1": "interestpercent_q1_sectioniii_finra", "alias2": "interestpercent_q1_sectioniii_finra"}, {"alias1": "checkbox_q1_sectioniii_finra", "alias2": "checkbox_q1_sectioniii_finra"}, {"alias1": "carveback_sectioniii_finra", "alias2": "carveback_sectioniii_finra"}, {"alias1": "percent_yes_carveback_sectioniii_finra", "alias2": "percent_yes_carveback_sectioniii_finra"}, {"alias1": "q3_sectioniii_finra", "alias2": "q3_sectioniii_finra"}, {"alias1": "q4_sectioniii_finra", "alias2": "q4_sectioniii_finra"}, {"alias1": "clarify_yes_q4_sectioniii_finra", "alias2": "clarify_yes_q4_sectioniii_finra"}, {"alias1": "sectioniva_finra", "alias2": "sectioniva_finra"}, {"alias1": "clarify_q1_sectioniva_finra", "alias2": "clarify_q1_sectioniva_finra"}, {"alias1": "clarify_q2_sectioniva_finra", "alias2": "clarify_q2_sectioniva_finra"}, {"alias1": "clarify_q3_sectioniva_finra", "alias2": "clarify_q3_sectioniva_finra"}, {"alias1": "sectionivb_finra", "alias2": "sectionivb_finra"}, {"alias1": "ischecked_checkbox_sectionivb_finra", "alias2": "ischecked_checkbox_sectionivb_finra"}, {"alias1": "q1_sectionivc_finra", "alias2": "q1_sectionivc_finra"}, {"alias1": "clarify_q1_sectionivc_finra", "alias2": "clarify_q1_sectionivc_finra"}, {"alias1": "q2_sectionivc_finra", "alias2": "q2_sectionivc_finra"}, {"alias1": "specify_op1_q2_sectionivc_finra", "alias2": "specify_op1_q2_sectionivc_finra"}, {"alias1": "specify_op2_q2_sectionivc_finra", "alias2": "specify_op2_q2_sectionivc_finra"}, {"alias1": "specify_op3_q2_sectionivc_finra", "alias2": "specify_op3_q2_sectionivc_finra"}, {"alias1": "specify_op4_q2_sectionivc_finra", "alias2": "specify_op4_q2_sectionivc_finra"}, {"alias1": "specifyifcheckanyoption_q2_sectionivc_finra", "alias2": "specifyifcheckanyoption_q2_sectionivc_finra"}, {"alias1": "noneofabove_sectionivd_finra", "alias2": "noneofabove_sectionivd_finra"}, {"alias1": "a_sectioni_finraaffiliation", "alias2": "a_sectioni_finraaffiliation"}, {"alias1": "name_isfinramember_a_sectioni_finraaffiliation", "alias2": "name_isfinramember_a_sectioni_finraaffiliation"}, {"alias1": "name_associatedperson_a_sectioni_finraaffiliation", "alias2": "name_associatedperson_a_sectioni_finraaffiliation"}, {"alias1": "name_familymember_a_sectioni_finraaffiliation", "alias2": "name_familymember_a_sectioni_finraaffiliation"}, {"alias1": "name_afiliate_a_sectioni_finraaffiliation", "alias2": "name_afiliate_a_sectioni_finraaffiliation"}, {"alias1": "name_q5_a_sectioni_finraaffiliation", "alias2": "name_q5_a_sectioni_finraaffiliation"}, {"alias1": "describe_noneofabove_a_sectioni_finraaffiliation", "alias2": "describe_noneofabove_a_sectioni_finraaffiliation"}, {"alias1": "b_sectioni_finraaffiliation", "alias2": "b_sectioni_finraaffiliation"}, {"alias1": "sectionii_finraaffiliation", "alias2": "sectionii_finraaffiliation"}, {"alias1": "q1_sectioniii_finraaffiliation", "alias2": "q1_sectioniii_finraaffiliation"}, {"alias1": "q2_sectioniii_finraaffiliation", "alias2": "q2_sectioniii_finraaffiliation"}, {"alias1": "multiplecheckbox42", "alias2": "multiplecheckbox42"}, {"alias1": "q3_sectioniii_finraaffiliation", "alias2": "q3_sectioniii_finraaffiliation"}, {"alias1": "a_q3_sectioniii_finraaffiliation", "alias2": "a_q3_sectioniii_finraaffiliation"}, {"alias1": "repeatable1", "alias2": "repeatable1"}, {"alias1": "totalhidden", "alias2": "totalhidden"}, {"alias1": "c_q3_sectioniii_finraaffiliation", "alias2": "c_q3_sectioniii_finraaffiliation"}, {"alias1": "q1_finra2111", "alias2": "q1_finra2111"}, {"alias1": "q2_finra2111", "alias2": "q2_finra2111"}, {"alias1": "part1_planinvestor", "alias2": "part1_planinvestor"}, {"alias1": "percentage1_ii_part1_planinvestor", "alias2": "percentage1_ii_part1_planinvestor"}, {"alias1": "percentage2_ii_part1_planinvestor", "alias2": "percentage2_ii_part1_planinvestor"}, {"alias1": "checked_iii_part1_planinvestor", "alias2": "checked_iii_part1_planinvestor"}, {"alias1": "identify_yes_checked_iii_part1_planinvestor", "alias2": "identify_yes_checked_iii_part1_planinvestor"}, {"alias1": "part2_planinvestor", "alias2": "part2_planinvestor"}, {"alias1": "part1_othereligibility", "alias2": "part1_othereligibility"}, {"alias1": "part2_othereligibility", "alias2": "part2_othereligibility"}, {"alias1": "identify_iv_part2_othereligibility", "alias2": "identify_iv_part2_othereligibility"}, {"alias1": "indicate_v_part2_othereligibility", "alias2": "indicate_v_part2_othereligibility"}, {"alias1": "asa_formpf_formpf", "alias2": "asa_formpf_formpf"}, {"alias1": "dropdown5", "alias2": "dropdown5"}, {"alias1": "asa_specify_other_formpf", "alias2": "asa_specify_other_formpf"}, {"alias1": "fundoffunds_part2_secreport", "alias2": "fundoffunds_part2_secreport"}, {"alias1": "radio4", "alias2": "radio4"}, {"alias1": "i_governmententitystatus", "alias2": "i_governmententitystatus"}, {"alias1": "ii_governmententitystatus", "alias2": "ii_governmententitystatus"}, {"alias1": "iii_governmententitystatus", "alias2": "iii_governmententitystatus"}, {"alias1": "repeatable2", "alias2": "repeatable2"}, {"alias1": "a_secrule506", "alias2": "a_secrule506"}, {"alias1": "b_secrule506", "alias2": "b_secrule506"}, {"alias1": "c_secrule506", "alias2": "c_secrule506"}, {"alias1": "d_secrule506", "alias2": "d_secrule506"}, {"alias1": "e_secrule506", "alias2": "e_secrule506"}, {"alias1": "f_secrule506", "alias2": "f_secrule506"}, {"alias1": "g_secrule506", "alias2": "g_secrule506"}, {"alias1": "h_secrule506", "alias2": "h_secrule506"}, {"alias1": "i_secrule506", "alias2": "i_secrule506"}, {"alias1": "na_indi_foreignpersonstatus", "alias2": "na_indi_foreignpersonstatus"}, {"alias1": "na_notindi_foreignpersonstatus", "alias2": "na_notindi_foreignpersonstatus"}, {"alias1": "indi_foreignpersonstatus", "alias2": "indi_foreignpersonstatus"}, {"alias1": "specify_op1_indi_foreignpersonstatus", "alias2": "specify_op1_indi_foreignpersonstatus"}, {"alias1": "specify_op2_indi_foreignpersonstatus", "alias2": "specify_op2_indi_foreignpersonstatus"}, {"alias1": "specify_op3_indi_foreignpersonstatus", "alias2": "specify_op3_indi_foreignpersonstatus"}, {"alias1": "part1_notindi_foreignpersonstatus", "alias2": "part1_notindi_foreignpersonstatus"}, {"alias1": "identify_op2_part1_notindi_foreignpersonstatus", "alias2": "identify_op2_part1_notindi_foreignpersonstatus"}, {"alias1": "identify_op4_part1_notindi_foreignpersonstatus", "alias2": "identify_op4_part1_notindi_foreignpersonstatus"}, {"alias1": "explain_op5_part1_notindi_foreignperson<PERSON>tus", "alias2": "explain_op5_part1_notindi_foreignperson<PERSON>tus"}, {"alias1": "explain_op6_part1_notindi_foreignperson<PERSON>tus", "alias2": "explain_op6_part1_notindi_foreignperson<PERSON>tus"}, {"alias1": "part2_notindi_foreignpersonstatus", "alias2": "part2_notindi_foreignpersonstatus"}, {"alias1": "identify_true_part2_notindi_foreignpersonstatus", "alias2": "identify_true_part2_notindi_foreignpersonstatus"}, {"alias1": "na_canadianinvestor", "alias2": "na_canadianinvestor"}, {"alias1": "canadianinvestor", "alias2": "canadianinvestor"}, {"alias1": "na_eeaukswiss", "alias2": "na_eeaukswiss"}, {"alias1": "a_parti_generalinstructions_eeaukswiss", "alias2": "a_parti_generalinstructions_eeaukswiss"}, {"alias1": "dropdown1", "alias2": "dropdown1"}, {"alias1": "b_parti_generalinstructions_eeaukswiss", "alias2": "b_parti_generalinstructions_eeaukswiss"}, {"alias1": "name_yes_b_parti_generalinstructions_eeaukswiss", "alias2": "name_yes_b_parti_generalinstructions_eeaukswiss"}, {"alias1": "position_yes_b_parti_generalinstructions_eeaukswiss", "alias2": "position_yes_b_parti_generalinstructions_eeaukswiss"}, {"alias1": "corpstatus_yes_b_parti_generalinstructions_eeaukswiss", "alias2": "corpstatus_yes_b_parti_generalinstructions_eeaukswiss"}, {"alias1": "dropdown2", "alias2": "dropdown2"}, {"alias1": "partii_generalinstructions_eeaukswiss", "alias2": "partii_generalinstructions_eeaukswiss"}, {"alias1": "req_l_partii_generalinstructions_eeaukswiss", "alias2": "req_l_partii_generalinstructions_eeaukswiss"}, {"alias1": "partiii_generalinstructions_eeaukswiss", "alias2": "partiii_generalinstructions_eeaukswiss"}, {"alias1": "categories_a_partiii_generalinstructions_eeaukswiss", "alias2": "categories_a_partiii_generalinstructions_eeaukswiss"}, {"alias1": "frenchinvestorstatus", "alias2": "frenchinvestorstatus"}, {"alias1": "notapplicable_eeaandukinvestorstatus", "alias2": "notapplicable_eeaandukinvestorstatus"}, {"alias1": "yes_eeaandukinvestorstatus", "alias2": "yes_eeaandukinvestorstatus"}, {"alias1": "qualifiedinvestorstatus_swissinvestorstatus", "alias2": "qualifiedinvestorstatus_swissinvestorstatus"}, {"alias1": "qualifiedinvestorandprofessionalclient_swissinvestorstatus", "alias2": "qualifiedinvestorandprofessionalclient_swissinvestorstatus"}, {"alias1": "swissexemptinvestors_swissinvestorstatus", "alias2": "swissexemptinvestors_swissinvestorstatus"}, {"alias1": "printname_eeaukswiss", "alias2": "printname_eeaukswiss"}, {"alias1": "usperson_partb_investorquestionnaire1", "alias2": "usperson_partb_investorquestionnaire1"}, {"alias1": "na_eeaukswiss3", "alias2": "na_eeaukswiss3"}, {"alias1": "taxyear_partb_investorquestionnaire1", "alias2": "taxyear_partb_investorquestionnaire1"}, {"alias1": "specify_othertaxableyear_taxyear_partb_investorquestionnaire1", "alias2": "specify_othertaxableyear_taxyear_partb_investorquestionnaire1"}, {"alias1": "individual_accreditedinvestor1", "alias2": "individual_accreditedinvestor1"}, {"alias1": "trust_accreditedinvestor1", "alias2": "trust_accreditedinvestor1"}, {"alias1": "grantors_revocable_trust_accreditedinvestor1", "alias2": "grantors_revocable_trust_accreditedinvestor1"}, {"alias1": "entityothertthantrust_accreditedinvestor1", "alias2": "entityothertthantrust_accreditedinvestor1"}, {"alias1": "entity_qualifiedpurchaser1", "alias2": "entity_qualifiedpurchaser1"}, {"alias1": "subscriberis_sectionia_finra1", "alias2": "subscriberis_sectionia_finra1"}, {"alias1": "describe_notrevoked_subscriberis_sectionia_finra1", "alias2": "describe_notrevoked_subscriberis_sectionia_finra1"}, {"alias1": "noneofabove_sectionib_finra1", "alias2": "noneofabove_sectionib_finra1"}, {"alias1": "subscriberis_sectioniia_finra1", "alias2": "subscriberis_sectioniia_finra1"}, {"alias1": "a_immidiatefamilymember_subscriberis_sectioniia_finra1", "alias2": "a_immidiatefamilymember_subscriberis_sectioniia_finra1"}, {"alias1": "b_immidiatefamilymember_subscriberis_sectioniia_finra1", "alias2": "b_immidiatefamilymember_subscriberis_sectioniia_finra1"}, {"alias1": "clarify_yes_b_immidiatefamilymember_subscriberis_sectioniia_finra1", "alias2": "clarify_yes_b_immidiatefamilymember_subscriberis_sectioniia_finra1"}, {"alias1": "c_immidiatefamilymember_subscriberis_sectioniia_finra1", "alias2": "c_immidiatefamilymember_subscriberis_sectioniia_finra1"}, {"alias1": "clarify_yes_c_immidiatefamilymember_subscriberis_sectioniia_finra1", "alias2": "clarify_yes_c_immidiatefamilymember_subscriberis_sectioniia_finra1"}, {"alias1": "clarify_finder_subscriberis_sectioniia_finra1", "alias2": "clarify_finder_subscriberis_sectioniia_finra1"}, {"alias1": "clarify_item6_subscriberis_sectioniia_finra1", "alias2": "clarify_item6_subscriberis_sectioniia_finra1"}, {"alias1": "a_item12_subscriberis_sectioniia_finra1", "alias2": "a_item12_subscriberis_sectioniia_finra1"}, {"alias1": "b_item12_subscriberis_sectioniia_finra1", "alias2": "b_item12_subscriberis_sectioniia_finra1"}, {"alias1": "c_item12_subscriberis_sectioniia_finra1", "alias2": "c_item12_subscriberis_sectioniia_finra1"}, {"alias1": "clarify_yes_c_item12_subscriberis_sectioniia_finra1", "alias2": "clarify_yes_c_item12_subscriberis_sectioniia_finra1"}, {"alias1": "noneofabove_sectioniib_finra1", "alias2": "noneofabove_sectioniib_finra1"}, {"alias1": "interestpercent_q1_sectioniii_finra1", "alias2": "interestpercent_q1_sectioniii_finra1"}, {"alias1": "checkbox_q1_sectioniii_finra1", "alias2": "checkbox_q1_sectioniii_finra1"}, {"alias1": "carveback_sectioniii_finra1", "alias2": "carveback_sectioniii_finra1"}, {"alias1": "percent_yes_carveback_sectioniii_finra1", "alias2": "percent_yes_carveback_sectioniii_finra1"}, {"alias1": "q3_sectioniii_finra1", "alias2": "q3_sectioniii_finra1"}, {"alias1": "q4_sectioniii_finra1", "alias2": "q4_sectioniii_finra1"}, {"alias1": "clarify_yes_q4_sectioniii_finra1", "alias2": "clarify_yes_q4_sectioniii_finra1"}, {"alias1": "sectioniva_finra1", "alias2": "sectioniva_finra1"}, {"alias1": "clarify_q1_sectioniva_finra1", "alias2": "clarify_q1_sectioniva_finra1"}, {"alias1": "clarify_q2_sectioniva_finra1", "alias2": "clarify_q2_sectioniva_finra1"}, {"alias1": "clarify_q3_sectioniva_finra1", "alias2": "clarify_q3_sectioniva_finra1"}, {"alias1": "sectionivb_finra1", "alias2": "sectionivb_finra1"}, {"alias1": "ischecked_checkbox_sectionivb_finra1", "alias2": "ischecked_checkbox_sectionivb_finra1"}, {"alias1": "q1_sectionivc_finra1", "alias2": "q1_sectionivc_finra1"}, {"alias1": "clarify_q1_sectionivc_finra1", "alias2": "clarify_q1_sectionivc_finra1"}, {"alias1": "q2_sectionivc_finra1", "alias2": "q2_sectionivc_finra1"}, {"alias1": "specify_op1_q2_sectionivc_finra1", "alias2": "specify_op1_q2_sectionivc_finra1"}, {"alias1": "specify_op2_q2_sectionivc_finra1", "alias2": "specify_op2_q2_sectionivc_finra1"}, {"alias1": "specify_op3_q2_sectionivc_finra1", "alias2": "specify_op3_q2_sectionivc_finra1"}, {"alias1": "specify_op4_q2_sectionivc_finra1", "alias2": "specify_op4_q2_sectionivc_finra1"}, {"alias1": "specifyifcheckanyoption_q2_sectionivc_finra1", "alias2": "specifyifcheckanyoption_q2_sectionivc_finra1"}, {"alias1": "noneofabove_sectionivd_finra1", "alias2": "noneofabove_sectionivd_finra1"}, {"alias1": "a_sectioni_finraaffiliation1", "alias2": "a_sectioni_finraaffiliation1"}, {"alias1": "name_isfinramember_a_sectioni_finraaffiliation1", "alias2": "name_isfinramember_a_sectioni_finraaffiliation1"}, {"alias1": "name_associatedperson_a_sectioni_finraaffiliation1", "alias2": "name_associatedperson_a_sectioni_finraaffiliation1"}, {"alias1": "name_familymember_a_sectioni_finraaffiliation1", "alias2": "name_familymember_a_sectioni_finraaffiliation1"}, {"alias1": "name_afiliate_a_sectioni_finraaffiliation1", "alias2": "name_afiliate_a_sectioni_finraaffiliation1"}, {"alias1": "name_q5_a_sectioni_finraaffiliation1", "alias2": "name_q5_a_sectioni_finraaffiliation1"}, {"alias1": "describe_noneofabove_a_sectioni_finraaffiliation1", "alias2": "describe_noneofabove_a_sectioni_finraaffiliation1"}, {"alias1": "b_sectioni_finraaffiliation1", "alias2": "b_sectioni_finraaffiliation1"}, {"alias1": "sectionii_finraaffiliation1", "alias2": "sectionii_finraaffiliation1"}, {"alias1": "q1_sectioniii_finraaffiliation1", "alias2": "q1_sectioniii_finraaffiliation1"}, {"alias1": "q2_sectioniii_finraaffiliation1", "alias2": "q2_sectioniii_finraaffiliation1"}, {"alias1": "multiplecheckbox45", "alias2": "multiplecheckbox45"}, {"alias1": "q3_sectioniii_finraaffiliation1", "alias2": "q3_sectioniii_finraaffiliation1"}, {"alias1": "a_q3_sectioniii_finraaffiliation1", "alias2": "a_q3_sectioniii_finraaffiliation1"}, {"alias1": "repeatable5", "alias2": "repeatable5"}, {"alias1": "totalhidden1", "alias2": "totalhidden1"}, {"alias1": "c_q3_sectioniii_finraaffiliation1", "alias2": "c_q3_sectioniii_finraaffiliation1"}, {"alias1": "q1_finra2112", "alias2": "q1_finra2112"}, {"alias1": "q2_finra2112", "alias2": "q2_finra2112"}, {"alias1": "part1_planinvestor1", "alias2": "part1_planinvestor1"}, {"alias1": "percentage1_ii_part1_planinvestor1", "alias2": "percentage1_ii_part1_planinvestor1"}, {"alias1": "percentage2_ii_part1_planinvestor1", "alias2": "percentage2_ii_part1_planinvestor1"}, {"alias1": "checked_iii_part1_planinvestor1", "alias2": "checked_iii_part1_planinvestor1"}, {"alias1": "identify_yes_checked_iii_part1_planinvestor1", "alias2": "identify_yes_checked_iii_part1_planinvestor1"}, {"alias1": "part2_planinvestor1", "alias2": "part2_planinvestor1"}, {"alias1": "part1_othereligibility1", "alias2": "part1_othereligibility1"}, {"alias1": "part2_othereligibility2", "alias2": "part2_othereligibility2"}, {"alias1": "indicate_v_part2_othereligibility1", "alias2": "indicate_v_part2_othereligibility1"}, {"alias1": "asa_formpf_formpf1", "alias2": "asa_formpf_formpf1"}, {"alias1": "dropdown6", "alias2": "dropdown6"}, {"alias1": "fundoffunds_part2_secreport1", "alias2": "fundoffunds_part2_secreport1"}, {"alias1": "a_secrule507", "alias2": "a_secrule507"}, {"alias1": "b_secrule507", "alias2": "b_secrule507"}, {"alias1": "c_secrule507", "alias2": "c_secrule507"}, {"alias1": "d_secrule507", "alias2": "d_secrule507"}, {"alias1": "e_secrule507", "alias2": "e_secrule507"}, {"alias1": "f_secrule507", "alias2": "f_secrule507"}, {"alias1": "g_secrule507", "alias2": "g_secrule507"}, {"alias1": "h_secrule507", "alias2": "h_secrule507"}, {"alias1": "i_secrule507", "alias2": "i_secrule507"}, {"alias1": "indi_foreignpersonstatus1", "alias2": "indi_foreignpersonstatus1"}, {"alias1": "specify_op1_indi_foreignpersonstatus1", "alias2": "specify_op1_indi_foreignpersonstatus1"}, {"alias1": "specify_op2_indi_foreignpersonstatus1", "alias2": "specify_op2_indi_foreignpersonstatus1"}, {"alias1": "specify_op3_indi_foreignpersonstatus1", "alias2": "specify_op3_indi_foreignpersonstatus1"}, {"alias1": "part1_notindi_foreignpersonstatus1", "alias2": "part1_notindi_foreignpersonstatus1"}, {"alias1": "identify_op2_part1_notindi_foreignpersonstatus1", "alias2": "identify_op2_part1_notindi_foreignpersonstatus1"}, {"alias1": "identify_op4_part1_notindi_foreignpersonstatus1", "alias2": "identify_op4_part1_notindi_foreignpersonstatus1"}, {"alias1": "explain_op5_part1_notindi_foreignpersonstatus1", "alias2": "explain_op5_part1_notindi_foreignpersonstatus1"}, {"alias1": "explain_op6_part1_notindi_foreignpersonstatus1", "alias2": "explain_op6_part1_notindi_foreignpersonstatus1"}, {"alias1": "part2_notindi_foreignpersonstatus1", "alias2": "part2_notindi_foreignpersonstatus1"}, {"alias1": "identify_true_part2_notindi_foreignpersonstatus1", "alias2": "identify_true_part2_notindi_foreignpersonstatus1"}, {"alias1": "na_eeaukswiss1", "alias2": "na_eeaukswiss1"}, {"alias1": "a_parti_generalinstructions_eeaukswiss1", "alias2": "a_parti_generalinstructions_eeaukswiss1"}, {"alias1": "dropdown4", "alias2": "dropdown4"}, {"alias1": "b_parti_generalinstructions_eeaukswiss1", "alias2": "b_parti_generalinstructions_eeaukswiss1"}, {"alias1": "name_yes_b_parti_generalinstructions_eeaukswiss1", "alias2": "name_yes_b_parti_generalinstructions_eeaukswiss1"}, {"alias1": "position_yes_b_parti_generalinstructions_eeaukswiss1", "alias2": "position_yes_b_parti_generalinstructions_eeaukswiss1"}, {"alias1": "corpstatus_yes_b_parti_generalinstructions_eeaukswiss1", "alias2": "corpstatus_yes_b_parti_generalinstructions_eeaukswiss1"}, {"alias1": "dropdown3", "alias2": "dropdown3"}, {"alias1": "partii_generalinstructions_eeaukswiss1", "alias2": "partii_generalinstructions_eeaukswiss1"}, {"alias1": "req_l_partii_generalinstructions_eeaukswiss1", "alias2": "req_l_partii_generalinstructions_eeaukswiss1"}, {"alias1": "partiii_generalinstructions_eeaukswiss1", "alias2": "partiii_generalinstructions_eeaukswiss1"}, {"alias1": "categories_a_partiii_generalinstructions_eeaukswiss1", "alias2": "categories_a_partiii_generalinstructions_eeaukswiss1"}, {"alias1": "frenchinvestorstatus1", "alias2": "frenchinvestorstatus1"}, {"alias1": "notapplicable_eeaandukinvestorstatus1", "alias2": "notapplicable_eeaandukinvestorstatus1"}, {"alias1": "yes_eeaandukinvestorstatus1", "alias2": "yes_eeaandukinvestorstatus1"}, {"alias1": "qualifiedinvestorstatus_swissinvestorstatus1", "alias2": "qualifiedinvestorstatus_swissinvestorstatus1"}, {"alias1": "qualifiedinvestorandprofessionalclient_swissinvestorstatus1", "alias2": "qualifiedinvestorandprofessionalclient_swissinvestorstatus1"}, {"alias1": "swissexemptinvestors_swissinvestorstatus1", "alias2": "swissexemptinvestors_swissinvestorstatus1"}, {"alias1": "printname_eeaukswiss1", "alias2": "printname_eeaukswiss1"}, {"alias1": "typeofsecurity_riskacknowledgement", "alias2": "typeofsecurity_riskacknowledgement"}, {"alias1": "nameofissuer_riskacknowledgement", "alias2": "nameofissuer_riskacknowledgement"}, {"alias1": "purchasedfrom_riskacknowledgement", "alias2": "purchasedfrom_riskacknowledgement"}, {"alias1": "multiplecheckbox10", "alias2": "multiplecheckbox10"}, {"alias1": "investmentamount_riskacknowledgement", "alias2": "investmentamount_riskacknowledgement"}, {"alias1": "multiplecheckbox26", "alias2": "multiplecheckbox26"}, {"alias1": "nameofsalesperson_riskacknowledgement", "alias2": "nameofsalesperson_riskacknowledgement"}, {"alias1": "email_riskacknowledgement", "alias2": "email_riskacknowledgement"}, {"alias1": "phone_riskacknowledgement", "alias2": "phone_riskacknowledgement"}, {"alias1": "nameoffirm_riskacknowledgement", "alias2": "nameoffirm_riskacknowledgement"}, {"alias1": "nameoffund_riskacknowledgement", "alias2": "nameoffund_riskacknowledgement"}, {"alias1": "address_riskacknowledgement", "alias2": "address_riskacknowledgement"}, {"alias1": "telephone_riskacknowledgement", "alias2": "telephone_riskacknowledgement"}, {"alias1": "emailaddress_riskacknowledgement", "alias2": "emailaddress_riskacknowledgement"}, {"alias1": "sectionapplied_cddpartb", "alias2": "sectionapplied_cddpartb"}, {"alias1": "excludedfromlegalentity", "alias2": "excludedfromlegalentity"}, {"alias1": "us_excludedfromlegalentity", "alias2": "us_excludedfromlegalentity"}, {"alias1": "name_naturalperson_cddpartb", "alias2": "name_naturalperson_cddpartb"}, {"alias1": "dateofbirth_naturalperson_cddpartb", "alias2": "dateofbirth_naturalperson_cddpartb"}, {"alias1": "placeofbirth_naturalperson_cddpartb", "alias2": "placeofbirth_naturalperson_cddpartb"}, {"alias1": "nationality_naturalperson_cddpartb", "alias2": "nationality_naturalperson_cddpartb"}, {"alias1": "residentialaddress_naturalperson_cddpartb", "alias2": "residentialaddress_naturalperson_cddpartb"}, {"alias1": "timeliving_naturalperson_cddpartb", "alias2": "timeliving_naturalperson_cddpartb"}, {"alias1": "employernameandoccupation_naturalperson_cddpartb", "alias2": "employernameandoccupation_naturalperson_cddpartb"}, {"alias1": "asa_tin_generalinfo2", "alias2": "asa_tin_generalinfo2"}, {"alias1": "ssn21", "alias2": "ssn21"}, {"alias1": "ein4", "alias2": "ein4"}, {"alias1": "itin5", "alias2": "itin5"}, {"alias1": "textbox11", "alias2": "textbox11"}, {"alias1": "sourceoffunds_naturalperson_cddpartb", "alias2": "sourceoffunds_naturalperson_cddpartb"}, {"alias1": "asa_politicallyexposedpersons_amlquestionnaire", "alias2": "asa_politicallyexposedpersons_amlquestionnaire"}, {"alias1": "hasspouse_cddpartb", "alias2": "hasspouse_cddpartb"}, {"alias1": "name_spouse_naturalperson_cddpartb", "alias2": "name_spouse_naturalperson_cddpartb"}, {"alias1": "dob_spouse_naturalperson_cddpartb", "alias2": "dob_spouse_naturalperson_cddpartb"}, {"alias1": "pob_spouse_naturalperson_cddpartb", "alias2": "pob_spouse_naturalperson_cddpartb"}, {"alias1": "nationality_spouse_naturalperson_cddpartb", "alias2": "nationality_spouse_naturalperson_cddpartb"}, {"alias1": "residentialaddress_spouse_naturalperson_cddpartb", "alias2": "residentialaddress_spouse_naturalperson_cddpartb"}, {"alias1": "timeliving_spouse_naturalperson_cddpartb", "alias2": "timeliving_spouse_naturalperson_cddpartb"}, {"alias1": "employernameandoccupation_spouse_naturalperson_cddpartb", "alias2": "employernameandoccupation_spouse_naturalperson_cddpartb"}, {"alias1": "asa_tin_generalinfo5", "alias2": "asa_tin_generalinfo5"}, {"alias1": "ssn2", "alias2": "ssn2"}, {"alias1": "ein2", "alias2": "ein2"}, {"alias1": "itin2", "alias2": "itin2"}, {"alias1": "textbox25", "alias2": "textbox25"}, {"alias1": "sourceoffunds_spouse_naturalperson_cddpartb", "alias2": "sourceoffunds_spouse_naturalperson_cddpartb"}, {"alias1": "spouse_politicallyexposedpersons_amlquestionnaire", "alias2": "spouse_politicallyexposedpersons_amlquestionnaire"}, {"alias1": "asa_fullname_investorname_generalinfo_1", "alias2": "asa_fullname_investorname_generalinfo_1"}, {"alias1": "asa_dateofformation_generalinfo_1", "alias2": "asa_dateofformation_generalinfo_1"}, {"alias1": "countryofincorporation_subscriberinfo_privateentity", "alias2": "countryofincorporation_subscriberinfo_privateentity"}, {"alias1": "stateofincorporation_subscriberinfo_privateentity", "alias2": "stateofincorporation_subscriberinfo_privateentity"}, {"alias1": "asa_fulladdress_placeofbusiness_generalinfo_1", "alias2": "asa_fulladdress_placeofbusiness_generalinfo_1"}, {"alias1": "asa_tin_generalinfo3", "alias2": "asa_tin_generalinfo3"}, {"alias1": "ssn3", "alias2": "ssn3"}, {"alias1": "ein3", "alias2": "ein3"}, {"alias1": "itin3", "alias2": "itin3"}, {"alias1": "textbox7", "alias2": "textbox7"}, {"alias1": "businesspurpose_subscriberinfo_privateentity", "alias2": "businesspurpose_subscriberinfo_privateentity"}, {"alias1": "sourceoffunds_subscriberinfo_privateentity", "alias2": "sourceoffunds_subscriberinfo_privateentity"}, {"alias1": "beneficialowners_radio_privateentity", "alias2": "beneficialowners_radio_privateentity"}, {"alias1": "na_information_privateentity1", "alias2": "na_information_privateentity1"}, {"alias1": "na_information_privateentity_0", "alias2": "na_information_privateentity_0"}, {"alias1": "repeatable3", "alias2": "repeatable3"}, {"alias1": "na_information_privateentity_1", "alias2": "na_information_privateentity_1"}, {"alias1": "yes_providerecords_controlperson_privateentity", "alias2": "yes_providerecords_controlperson_privateentity"}, {"alias1": "specific_records_controlperson_privateentity", "alias2": "specific_records_controlperson_privateentity"}, {"alias1": "repeatable6", "alias2": "repeatable6"}, {"alias1": "multiplecheckbox5", "alias2": "multiplecheckbox5"}, {"alias1": "na_external_registration_privateentity", "alias2": "na_external_registration_privateentity"}, {"alias1": "na_entitymanagedbyexternalinvestmentmanager_privateentity", "alias2": "na_entitymanagedbyexternalinvestmentmanager_privateentity"}, {"alias1": "nomineeorcustodian_privateentity", "alias2": "nomineeorcustodian_privateentity"}, {"alias1": "asa_fullname_investorname_generalinfo_2", "alias2": "asa_fullname_investorname_generalinfo_2"}, {"alias1": "dateofregistration_subscriberinfo_foundation", "alias2": "dateofregistration_subscriberinfo_foundation"}, {"alias1": "countryofiregistration_subscriberinfo_foundation", "alias2": "countryofiregistration_subscriberinfo_foundation"}, {"alias1": "stateofregistration_subscriberinfo_foundation", "alias2": "stateofregistration_subscriberinfo_foundation"}, {"alias1": "asa_fulladdress_placeofbusiness_generalinfo_2", "alias2": "asa_fulladdress_placeofbusiness_generalinfo_2"}, {"alias1": "asa_tin_generalinfo7", "alias2": "asa_tin_generalinfo7"}, {"alias1": "ssn6", "alias2": "ssn6"}, {"alias1": "ein6", "alias2": "ein6"}, {"alias1": "itin9", "alias2": "itin9"}, {"alias1": "textbox17", "alias2": "textbox17"}, {"alias1": "foundationpurpose_subscriberinfo_foundation", "alias2": "foundationpurpose_subscriberinfo_foundation"}, {"alias1": "sourceoffunds_subscriberinfo_foundation", "alias2": "sourceoffunds_subscriberinfo_foundation"}, {"alias1": "beneficiariesname_subscriberinfo_foundation", "alias2": "beneficiariesname_subscriberinfo_foundation"}, {"alias1": "radio12", "alias2": "radio12"}, {"alias1": "yes_providerecords_controlperson_foundation", "alias2": "yes_providerecords_controlperson_foundation"}, {"alias1": "specific_records_controlperson_foundation", "alias2": "specific_records_controlperson_foundation"}, {"alias1": "repeatable7", "alias2": "repeatable7"}, {"alias1": "na_nomineeorcustodian_foundation", "alias2": "na_nomineeorcustodian_foundation"}, {"alias1": "asa_fullname_investorname_generalinfo_3", "alias2": "asa_fullname_investorname_generalinfo_3"}, {"alias1": "dateofestablishment_subscriberinfo_trust_otherthanstatutorytrust", "alias2": "dateofestablishment_subscriberinfo_trust_otherthanstatutorytrust"}, {"alias1": "fulladdress_trustee_subscriberinfo_trust_otherthanstatutorytrust", "alias2": "fulladdress_trustee_subscriberinfo_trust_otherthanstatutorytrust"}, {"alias1": "jurisdictionoftrust_subscriberinfo_trust_otherthanstatutorytrust", "alias2": "jurisdictionoftrust_subscriberinfo_trust_otherthanstatutorytrust"}, {"alias1": "asa_tin_generalinfo10", "alias2": "asa_tin_generalinfo10"}, {"alias1": "ssn8", "alias2": "ssn8"}, {"alias1": "ein8", "alias2": "ein8"}, {"alias1": "itin7", "alias2": "itin7"}, {"alias1": "textbox8", "alias2": "textbox8"}, {"alias1": "nameofsettlororgrantor_subscriberinfo_trust_otherthanstatutorytrust", "alias2": "nameofsettlororgrantor_subscriberinfo_trust_otherthanstatutorytrust"}, {"alias1": "sourceofsettlorfunds_subscriberinfo_trust_otherthanstatutorytrust", "alias2": "sourceofsettlorfunds_subscriberinfo_trust_otherthanstatutorytrust"}, {"alias1": "beneficiaries_subscriberinfo_trust_otherthanstatutorytrust", "alias2": "beneficiaries_subscriberinfo_trust_otherthanstatutorytrust"}, {"alias1": "yes_providerecords_controlperson_foundation1", "alias2": "yes_providerecords_controlperson_foundation1"}, {"alias1": "specific_records_controlperson_foundation1", "alias2": "specific_records_controlperson_foundation1"}, {"alias1": "repeatable8", "alias2": "repeatable8"}, {"alias1": "na_nomineeorcustodian_foundation1", "alias2": "na_nomineeorcustodian_foundation1"}, {"alias1": "asa_fullname_investorname_generalinfo_4", "alias2": "asa_fullname_investorname_generalinfo_4"}, {"alias1": "dateofregistration_subscriberinfo_statutorytrust", "alias2": "dateofregistration_subscriberinfo_statutorytrust"}, {"alias1": "fulladdress_trustee_subscriberinfo_subscriberinfo_statutorytrust", "alias2": "fulladdress_trustee_subscriberinfo_subscriberinfo_statutorytrust"}, {"alias1": "jurisdictionoftrust_subscriberinfo_subscriberinfo_statutorytrust", "alias2": "jurisdictionoftrust_subscriberinfo_subscriberinfo_statutorytrust"}, {"alias1": "asa_tin_generalinfo12", "alias2": "asa_tin_generalinfo12"}, {"alias1": "ssn10", "alias2": "ssn10"}, {"alias1": "ein10", "alias2": "ein10"}, {"alias1": "itin11", "alias2": "itin11"}, {"alias1": "textbox10", "alias2": "textbox10"}, {"alias1": "nameofsettlororgrantor_subscriberinfo_subscriberinfo_statutorytrust", "alias2": "nameofsettlororgrantor_subscriberinfo_subscriberinfo_statutorytrust"}, {"alias1": "sourceofsettlorfunds_subscriberinfo_subscriberinfo_statutorytrust", "alias2": "sourceofsettlorfunds_subscriberinfo_subscriberinfo_statutorytrust"}, {"alias1": "beneficiaries_subscriberinfo_subscriberinfo_statutorytrust", "alias2": "beneficiaries_subscriberinfo_subscriberinfo_statutorytrust"}, {"alias1": "yes_providerecords_controlperson_statutorytrust", "alias2": "yes_providerecords_controlperson_statutorytrust"}, {"alias1": "specific_records_controlperson_statutorytrust", "alias2": "specific_records_controlperson_statutorytrust"}, {"alias1": "repeatable9", "alias2": "repeatable9"}, {"alias1": "na_nomineeorcustodian_foundation2", "alias2": "na_nomineeorcustodian_foundation2"}, {"alias1": "asa_fullname_investorname_generalinfo_6", "alias2": "asa_fullname_investorname_generalinfo_6"}, {"alias1": "asa_dateofformation_generalinfo_2", "alias2": "asa_dateofformation_generalinfo_2"}, {"alias1": "countryofincorporation_subscriberinfo_financialentity", "alias2": "countryofincorporation_subscriberinfo_financialentity"}, {"alias1": "stateofincorporation_subscriberinfo_financialentity", "alias2": "stateofincorporation_subscriberinfo_financialentity"}, {"alias1": "textbox27", "alias2": "textbox27"}, {"alias1": "asa_fulladdress_placeofbusiness_generalinfo_4", "alias2": "asa_fulladdress_placeofbusiness_generalinfo_4"}, {"alias1": "asa_tin_generalinfo13", "alias2": "asa_tin_generalinfo13"}, {"alias1": "ssn12", "alias2": "ssn12"}, {"alias1": "ein21", "alias2": "ein21"}, {"alias1": "itin12", "alias2": "itin12"}, {"alias1": "textbox14", "alias2": "textbox14"}, {"alias1": "regulatoryauthority_subscriberinfo_financialentity", "alias2": "regulatoryauthority_subscriberinfo_financialentity"}, {"alias1": "asa_registrationnumber_generalinfo", "alias2": "asa_registrationnumber_generalinfo"}, {"alias1": "sourceoffunds_subscriberinfo_financialentity", "alias2": "sourceoffunds_subscriberinfo_financialentity"}, {"alias1": "na_nomineeorcustodian_foundation3", "alias2": "na_nomineeorcustodian_foundation3"}, {"alias1": "asa_fullname_investorname_generalinfo_7", "alias2": "asa_fullname_investorname_generalinfo_7"}, {"alias1": "asa_specify_othertypeofentities_investortype1", "alias2": "asa_specify_othertypeofentities_investortype1"}, {"alias1": "governmentsponsor_subscriberinfo_governmentrelatedentity", "alias2": "governmentsponsor_subscriberinfo_governmentrelatedentity"}, {"alias1": "fulladdress_registeredoffice_subscriberinfo_governmentrelatedentity", "alias2": "fulladdress_registeredoffice_subscriberinfo_governmentrelatedentity"}, {"alias1": "subscriberpurpose_subscriberinfo_governmentrelatedentity", "alias2": "subscriberpurpose_subscriberinfo_governmentrelatedentity"}, {"alias1": "asa_tin_generalinfo14", "alias2": "asa_tin_generalinfo14"}, {"alias1": "ssn13", "alias2": "ssn13"}, {"alias1": "ein13", "alias2": "ein13"}, {"alias1": "itin13", "alias2": "itin13"}, {"alias1": "textbox13", "alias2": "textbox13"}, {"alias1": "sourceoffunds_subscriberinfo_governmentrelatedentity", "alias2": "sourceoffunds_subscriberinfo_governmentrelatedentity"}, {"alias1": "yes_providerecords_controlperson_statutorytrust1", "alias2": "yes_providerecords_controlperson_statutorytrust1"}, {"alias1": "specific_records_controlperson_governmentrelatedentity", "alias2": "specific_records_controlperson_governmentrelatedentity"}, {"alias1": "fullname_controlperson_governmentrelatedentity", "alias2": "fullname_controlperson_governmentrelatedentity"}, {"alias1": "title_controlperson_governmentrelatedentity", "alias2": "title_controlperson_governmentrelatedentity"}, {"alias1": "fulladdress_controlperson_governmentrelatedentity", "alias2": "fulladdress_controlperson_governmentrelatedentity"}, {"alias1": "dateofbirth_controlperson_governmentrelatedentity", "alias2": "dateofbirth_controlperson_governmentrelatedentity"}, {"alias1": "na_nomineeorcustodian_governmentrelatedentity", "alias2": "na_nomineeorcustodian_governmentrelatedentity"}, {"alias1": "asa_fullname_investorname_generalinfo_8", "alias2": "asa_fullname_investorname_generalinfo_8"}, {"alias1": "dateofregistration_subscriberinfo_pensionplans", "alias2": "dateofregistration_subscriberinfo_pensionplans"}, {"alias1": "countryofregistration_subscriberinfo_pensionplans", "alias2": "countryofregistration_subscriberinfo_pensionplans"}, {"alias1": "stateofregistration_subscriberinfo_pensionplans", "alias2": "stateofregistration_subscriberinfo_pensionplans"}, {"alias1": "textbox28", "alias2": "textbox28"}, {"alias1": "asa_fulladdress_placeofbusiness_generalinfo_7", "alias2": "asa_fulladdress_placeofbusiness_generalinfo_7"}, {"alias1": "asa_tin_generalinfo15", "alias2": "asa_tin_generalinfo15"}, {"alias1": "ssn14", "alias2": "ssn14"}, {"alias1": "ein14", "alias2": "ein14"}, {"alias1": "itin14", "alias2": "itin14"}, {"alias1": "textbox24", "alias2": "textbox24"}, {"alias1": "sourceoffunds_subscriberinfo_pensionplans", "alias2": "sourceoffunds_subscriberinfo_pensionplans"}, {"alias1": "describeplanbeneficiaries_subscriberinfo_pensionplans", "alias2": "describeplanbeneficiaries_subscriberinfo_pensionplans"}, {"alias1": "requireddocuments_checkbox_subscriberinfo_pensionplan", "alias2": "requireddocuments_checkbox_subscriberinfo_pensionplan"}, {"alias1": "radio13", "alias2": "radio13"}, {"alias1": "yes_providerecords_controlperson_pensionplan", "alias2": "yes_providerecords_controlperson_pensionplan"}, {"alias1": "specific_records_controlperson_pensionplan", "alias2": "specific_records_controlperson_pensionplan"}, {"alias1": "fullname_controlperson_pensionplan", "alias2": "fullname_controlperson_pensionplan"}, {"alias1": "title_controlperson_pensionplan", "alias2": "title_controlperson_pensionplan"}, {"alias1": "fulladdress_controlperson_pensionplan", "alias2": "fulladdress_controlperson_pensionplan"}, {"alias1": "dateofbirth_controlperson_pensionplan", "alias2": "dateofbirth_controlperson_pensionplan"}, {"alias1": "asa_tin_generalinfo9", "alias2": "asa_tin_generalinfo9"}, {"alias1": "ssn9", "alias2": "ssn9"}, {"alias1": "ein9", "alias2": "ein9"}, {"alias1": "itin21", "alias2": "itin21"}, {"alias1": "textbox15", "alias2": "textbox15"}, {"alias1": "na_nomineeorcustodian_pensionplan", "alias2": "na_nomineeorcustodian_pensionplan"}, {"alias1": "asa_fullname_investorname_generalinfo_9", "alias2": "asa_fullname_investorname_generalinfo_9"}, {"alias1": "statesponsor_subscriberinfo_sovereignwealthfunds", "alias2": "statesponsor_subscriberinfo_sovereignwealthfunds"}, {"alias1": "legalstatus_subscriberinfo_sovereignwealthfunds", "alias2": "legalstatus_subscriberinfo_sovereignwealthfunds"}, {"alias1": "dateofregistrationorcreation_subscriberinfo_sovereignwealthfunds", "alias2": "dateofregistrationorcreation_subscriberinfo_sovereignwealthfunds"}, {"alias1": "fundmanager_subscriberinfo_sovereignwealthfunds", "alias2": "fundmanager_subscriberinfo_sovereignwealthfunds"}, {"alias1": "fulladdress_fundmanager_sovereignwealthfunds", "alias2": "fulladdress_fundmanager_sovereignwealthfunds"}, {"alias1": "purposeofthefund_subscriberinfo_sovereignwealthfunds", "alias2": "purposeofthefund_subscriberinfo_sovereignwealthfunds"}, {"alias1": "asa_tin_generalinfo16", "alias2": "asa_tin_generalinfo16"}, {"alias1": "ssn15", "alias2": "ssn15"}, {"alias1": "ein15", "alias2": "ein15"}, {"alias1": "itin15", "alias2": "itin15"}, {"alias1": "textbox16", "alias2": "textbox16"}, {"alias1": "sourceoffund_subscriberinfo_sovereignwealthfunds", "alias2": "sourceoffund_subscriberinfo_sovereignwealthfunds"}, {"alias1": "radio14", "alias2": "radio14"}, {"alias1": "yes_providerecords_controlperson_sovereignwealthfunds", "alias2": "yes_providerecords_controlperson_sovereignwealthfunds"}, {"alias1": "specific_records_controlperson_sovereignwealthfunds", "alias2": "specific_records_controlperson_sovereignwealthfunds"}, {"alias1": "fullname_controlperson_sovereignwealthfunds", "alias2": "fullname_controlperson_sovereignwealthfunds"}, {"alias1": "title_controlperson_sovereignwealthfunds", "alias2": "title_controlperson_sovereignwealthfunds"}, {"alias1": "fulladdress_controlperson_sovereignwealthfunds", "alias2": "fulladdress_controlperson_sovereignwealthfunds"}, {"alias1": "dateofbirth_controlperson_sovereignwealthfunds", "alias2": "dateofbirth_controlperson_sovereignwealthfunds"}, {"alias1": "asa_tin_generalinfo17", "alias2": "asa_tin_generalinfo17"}, {"alias1": "ssn16", "alias2": "ssn16"}, {"alias1": "ein16", "alias2": "ein16"}, {"alias1": "itin16", "alias2": "itin16"}, {"alias1": "textbox18", "alias2": "textbox18"}, {"alias1": "legalentity_identificationnumber_controlperson_sovereignwealthfunds", "alias2": "legalentity_identificationnumber_controlperson_sovereignwealthfunds"}, {"alias1": "na_nomineeorcustodian_sovereignwealthfunds", "alias2": "na_nomineeorcustodian_sovereignwealthfunds"}, {"alias1": "asa_fullname_investorname_generalinfo_10", "alias2": "asa_fullname_investorname_generalinfo_10"}, {"alias1": "asa_dateofformation_generalinfo_3", "alias2": "asa_dateofformation_generalinfo_3"}, {"alias1": "countryofincorporation_subscriberinfo_listedentity", "alias2": "countryofincorporation_subscriberinfo_listedentity"}, {"alias1": "stateofincorporation_subscriberinfo_listedentity", "alias2": "stateofincorporation_subscriberinfo_listedentity"}, {"alias1": "asa_fulladdress_placeofbusiness_generalinfo_6", "alias2": "asa_fulladdress_placeofbusiness_generalinfo_6"}, {"alias1": "asa_tin_generalinfo18", "alias2": "asa_tin_generalinfo18"}, {"alias1": "ssn17", "alias2": "ssn17"}, {"alias1": "ein17", "alias2": "ein17"}, {"alias1": "itin17", "alias2": "itin17"}, {"alias1": "textbox23", "alias2": "textbox23"}, {"alias1": "fullname_stockexchange_subscriberinfo_listedentity", "alias2": "fullname_stockexchange_subscriberinfo_listedentity"}, {"alias1": "tickersymbol_subscriberinfo_listedentity", "alias2": "tickersymbol_subscriberinfo_listedentity"}, {"alias1": "sourceoffunds_subscriberinfo_listedentity", "alias2": "sourceoffunds_subscriberinfo_listedentity"}, {"alias1": "na_nomineeorcustodian_listedentity", "alias2": "na_nomineeorcustodian_listedentity"}, {"alias1": "indi_touched", "alias2": "indi_touched"}, {"alias1": "is_joint", "alias2": "is_joint"}, {"alias1": "is_np_not_joint", "alias2": "is_np_not_joint"}, {"alias1": "is_slp", "alias2": "is_slp"}, {"alias1": "is_rlp", "alias2": "is_rlp"}]