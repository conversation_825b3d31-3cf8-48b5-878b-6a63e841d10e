# White Tree Validation

![The White Tree Validation](wikis/the-white-tree-of-gondor.webp "The White Tree Validation")

## Description

A place to contain internal tools for support Anduin members. Mainly related to FormBuilder and PDF Tool.
Hope it will be useful for us in management, delivery and development these kind of tools.

Notion document [here](https://www.notion.so/anduin/White-Tree-All-in-one-digitization-internal-tools-1af3f653b1df80dc9078cb5f8655f145).

## Table of Contents

- [Installation](#installation)
- [Usage](#usage)
- [Features](#features)
- [Technologies Used](#technologies-used)
- [Contributing](#contributing)
- [Contact](#contact)

## Installation

Provide step-by-step instructions on how to set up your project locally.

1. **Clone the repository:**

   ```bash
   <NAME_EMAIL>:anduintransaction/white-tree-validation.git
   ```
   
2. **Install dependencies:**

   ```bash
   npm install
   ```

## Usage
For development, run the following command:

   ```bash
   npm run dev
   ```
Then you can use the [localhost:5173](http://localhost:5173/) to open the UI.

![demo](wikis/demo.png "demo")

Build:

   ```bash
   npm run build
   ```

To preview the production build:
   ```bash
   npm run preview
   ```

Production URL: [https://white-tree.anduin.center/](https://white-tree.anduin.center/)

## Features

- UI: Dark mode, collapsible sidebar, etc.
- [Digitization Effort Estimator](https://www.notion.so/anduin/Automating-Effort-Estimation-for-New-Forms-1af3f653b1df80fca37fdb7f5fb96a95)
- [UI Bugs Detector](https://www.notion.so/anduin/Utilizing-JS-code-and-Postman-to-promptly-detect-UI-bugs-potential-bugs-ec548f94a2ee4adf97ae38e339b65291?d=0a48f183d23d460e948fd437a4d9fcfb#0a98531d0b8048708525f65f5b7b38d3)
- [Compare Mapping Widgets Form-To-Form](https://www.notion.so/anduin/Tooling-to-compare-mapping-widgets-between-2-forms-for-IDM-82a39d1b75604f648a7f8a9ac7bda2e5)
- [Font Mismatch Detector](https://www.notion.so/anduin/Use-json-file-in-PDF-tool-to-verify-any-properties-such-as-font-font-size-for-global-check-fro-12f3f653b1df80dd8c72d0f793071160)
- [Form To Blueprint Transformation](https://www.notion.so/anduin/White-Tree-All-in-one-digitization-internal-tools-1af3f653b1df80dc9078cb5f8655f145?pvs=4#1b63f653b1df80489711f59736d6aa4d)
- And more tools to come soon...

## Technologies Used

- NodeJS
- UI: HTML5, CSS3, JavaScript, BootStrap 5
- Hosting, domain, authentication: Cloudflare R2
- Version Control: GitHub
- CI/CD: GitHub Actions

## Contributing

1. **Clone the repository:**
   ```bash
   <NAME_EMAIL>:anduintransaction/white-tree-validation.git
   ```

2. **Create a new branch:**
   ```bash
   git checkout -b my-branch
   ```

3. **Make your changes**

4. **Commit your changes:**
   ```bash
   git commit -m "Add feature"
   ```

5. **Push your changes:**
   ```bash
   git push origin my-branch
   ```

## Contact

- Feature Supporters: [@Nghia](https://anduin.slack.com/team/U04LTKLP49L), [@Quan](https://anduin.slack.com/team/U0306FKAGFR), [@BaoCong](https://anduin.slack.com/team/U05BK017U48),...
- Deployment Supporter: [@Thanh Le](https://anduin.slack.com/team/U03UJSQ2RJN)