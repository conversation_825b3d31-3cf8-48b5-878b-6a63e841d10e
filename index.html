<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digitization | White Tree</title>
    <link rel="icon" href="/wikis/white-tree-icon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="/src/styles/main.css">
</head>
<body>
<nav class="navbar navbar-expand-lg fixed-top">
    <div class="container-fluid bg-primary">
        <a class="navbar-brand" href="#">
            <img src="wikis/white-tree-logo.png" alt="Logo" width="128"
                 height="32">
        </a>
        <button class="btn btn-outline-dark ms-auto" id="themeToggle">
            <i class="bi bi-moon-stars-fill"></i>
        </button>
    </div>
</nav>

<div class="wrapper content-wrapper">

    <!--Left Sidebar-->
    <nav class="sidebar p-3 border-end" id="sidebar">

        <!--Left Sidebar Header-->
        <div class="sidebar-header pb-3 d-flex justify-content-between align-items-center">
            <h3 id="welcomeHeader" class="sidebar-title mb-0">Tools List</h3>
            <button class="btn btn-link sidebar-toggle p-0 justify-content-between align-items-center" type="button"
                    aria-label="Toggle Sidebar">
                <i class="bi bi-list fs-4"></i>
            </button>
        </div>

        <!--Left Sidebar Body-->
        <div class="mt-3 overflow-hidden">
            <div class="mb-3">
                <div class="mb-2 d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-door-open mx-2"></i>
                        <label class="form-label menu-to-hide">Server:</label>
                    </div>
                    <select id="serverSelection" class="form-select ms-3 menu-to-hide">
                        <option value="US">US</option>
                        <option value="EU">EU</option>
                    </select>
                </div>

                <div class="mb-2 d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-menu-button-wide-fill mx-2"></i>
                        <label class="form-label menu-to-hide">Environment:</label>
                    </div>
                    <select id="environmentSelection" class="form-select ms-3 menu-to-hide">
                        <option value="Deals">Deals</option>
                        <option value="Minas">Minas</option>
                    </select>
                </div>

                <div class="mb-2 d-flex align-items-top flex-column">
                    <div class="d-flex w-100">
                        <i class="bi bi-key mx-2"></i>
                        <label class="menu-to-hide">Bearer Token:</label>
                    </div>
                    <input id="bearerTokenInput" type="text" class="form-control menu-to-hide w-100 mt-1">
                </div>
            </div>

            <!--All internal tools in the left sidebar-->
            <div class="d-grid gap-3 card-container overflow-auto">
                <div class="tool-block card" id="estimatorCard">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-hourglass-split ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Digitization Effort Estimator</h6>
                                <p class="small text-muted mb-0">Scan the JSON file from the PDF Tool for predicting the needed digitization effort.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">PDF Tool</span>
                                    <span class="badge bg-primary">Testing</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card" id="uiBugsDetectorCard">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-search ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">UI Bugs Detector</h6>
                                <p class="small text-muted mb-0">Reading form JSON file within Form Builder to detect potential UI bugs.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">Form</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card" id="compareMappingWidgetsFormToFormCard">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-terminal-split ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Compare Mapping Widgets Form-to-Form</h6>
                                <p class="small text-muted mb-0">A tool that previews widgets between 2 forms via the Export template, helping users stay aware of field changes when exporting the Form Export Mappings.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">Form</span>
                                    <span class="badge bg-success">Mapping</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card" id="fontMismatchDetectorCard">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-file-earmark-font ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Font Mismatch Detector</h6>
                                <p class="small text-muted mb-2">Scan the JSON file from the PDF Tool for detecting font family mismatch.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">PDF Tool</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card" id="formToBlueprintCard">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-diagram-3 ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Transfer Form widget to Blueprint metadata</h6>
                                <p class="small text-muted mb-0">Reduce manual work when transform blueprint for non-checklist source form.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">Blueprint</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card" id="kkrSubFundCustomIdCheckCard">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-check-circle ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">KKR Sub-fund Custom ID Check</h6>
                                <p class="small text-muted mb-0">Validate that KKR sub-funds have custom ID properties by checking entity data and fund information.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">FundSub</span>
                                    <span class="badge bg-primary">API</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-patch-question-fill ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Add Tax Forms to Fund</h6>
                                <p class="small text-muted mb-0">Aiming to speed up the fund setup process.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">FundSub</span>
                                    <span class="badge bg-warning">To Do</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-diagram-3 ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Generate Events for Repeatable</h6>
                                <p class="small text-muted mb-0">Reduce manual work by generate test events for repeatable items.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">Form</span>
                                    <span class="badge bg-warning">To Do</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-diagram-3 ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">ASA Duplication Validation</h6>
                                <p class="small text-muted mb-0">Automatically detect duplicated ASA mapping.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">Form</span>
                                    <span class="badge bg-warning">To Do</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-patch-question-fill ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Batch rename PDF fields</h6>
                                <p class="small text-muted mb-0">Batch renaming in just one-click, such as adding prefix/postfix to all PDF fields.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-success">PDF Tool</span>
                                    <span class="badge bg-warning">To Do</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tool-block card">
                    <div class="d-flex justify-content-between align-items-center">
                        <i class="bi bi-patch-question-fill ms-2"></i>
                        <div class="card-body menu-to-hide">
                            <div class="d-flex flex-column justify-content-between align-items-start" style="height: 100%;">
                                <h6 class="mb-1">Dummy</h6>
                                <p class="small text-muted mb-0">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec cursus.</p>
                                <div class="badges-container" style="align-self: flex-end;">
                                    <span class="badge bg-warning">To Do</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!--/ All internal tools in the left sidebar  -->
        </div>

    </nav>
    <!--/ Left Sidebar-->

    <!--Main Content-->
    <div id="contentArea" class="content-area">

        <!--Content Body-->
        <div id="mainContent" class="container-fluid p-4">
            <div id="welcomeContent" class="content-render" style="display: block;"></div>
            <div id="estimatorContent" class="content-render" style="display: none;"></div>
            <div id="uiBugsDetectorContent" class="content-render" style="display: none;"></div>
            <div id="compareMappingWidgetsFormToFormContent" class="content-render" style="display: none;"></div>
            <div id="fontMismatchDetectorContent" class="content-render" style="display: none;"></div>
            <div id="formToBlueprintContent" class="content-render" style="display: none;"></div>
            <div id="kkrSubFundCustomIdCheckContent" class="content-render" style="display: none;"></div>
        </div>
        <!--/ Content Body-->

    </div>
    <!--Main Content-->

    <!--Toast message-->
    <div id="toastMsg" style="display: none;">The text has been copied to clipboard.</div>

</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables must be loaded after jQuery -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="module" src="/src/main.js"></script>
</body>
</html>