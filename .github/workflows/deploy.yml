name: Build and Deploy to Cloudflare R2

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: white-tree

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "lts/*" # Or specify a specific version

      - name: Install dependencies
        run: npm install

      - name: Build app
        run: npm run build # Replace with your build command

      - name: Configure rclone
        run: |
          mkdir -p ~/.config/rclone
          echo "[r2]" > ~/.config/rclone/rclone.conf
          echo "type = s3" >> ~/.config/rclone/rclone.conf
          echo "provider = Cloudflare" >> ~/.config/rclone/rclone.conf
          echo "access_key_id = ${{ secrets.R2_ACCESS_KEY_ID }}" >> ~/.config/rclone/rclone.conf
          echo "secret_access_key = ${{ secrets.R2_SECRET_ACCESS_KEY }}" >> ~/.config/rclone/rclone.conf
          echo "endpoint = ${{ secrets.R2_ENDPOINT }}" >> ~/.config/rclone/rclone.conf

      - name: Upload dist folder to Cloudflare R2
        run: rclone copy ./dist/ r2:${{ secrets.R2_BUCKET_NAME }}/ --progress
